/* PROFILE PAGE - ORGANIZED & CLEAN */

/* Page Container */
.profile-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-4);
  min-height: 100vh;
}

.profile-header {
  margin-bottom: var(--space-8);
  text-align: center;
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.profile-header h1 {
  font-size: 2.5rem;
  color: var(--white);
  margin-bottom: var(--space-3);
  font-weight: 700;
  position: relative;
}

.profile-header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.profile-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Alerts */
.alert {
  padding: var(--space-4);
  border-radius: 8px;
  margin-bottom: var(--space-6);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.alert-success {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.alert-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Profile Container */
.profile-container {
  display: flex;
  gap: var(--space-8);
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--border);
}

/* Profile Sidebar */
.profile-sidebar {
  width: 320px;
  background: var(--gray-light);
  padding: var(--space-6);
  border-right: 1px solid var(--border);
}

/* Profile Avatar Section */
.profile-avatar {
  text-align: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border);
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--red);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 auto var(--space-3);
  border: 4px solid var(--white);
  box-shadow: var(--shadow);
}

.profile-avatar h3 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-1);
  font-weight: 600;
}

.user-email {
  color: var(--gray);
  font-size: 0.9rem;
  margin: 0;
}

/* Profile Menu */
.profile-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.profile-menu li {
  margin-bottom: var(--space-2);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s;
}

.profile-menu li:hover {
  background: rgba(220, 38, 38, 0.1);
}

.profile-menu li.active {
  background: var(--red);
  color: var(--white);
}

.profile-menu li.active .menu-icon svg,
.profile-menu li.active {
  color: var(--white);
}

.profile-menu li {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: var(--gray);
  font-weight: 500;
  font-size: 0.95rem;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
}

.menu-icon svg {
  font-size: 1.1rem;
  color: var(--gray);
  transition: color 0.2s;
}

.profile-menu li:hover .menu-icon svg {
  color: var(--red);
}

.profile-menu li.active .menu-icon svg {
  color: var(--white);
}

/* Menu Link Styling */
.menu-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: inherit;
  text-decoration: none;
  width: 100%;
}

.menu-link:hover {
  color: var(--red);
}

/* Logout Item */
.logout-item {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border);
}

.logout-item:hover {
  background: rgba(220, 38, 38, 0.1);
  color: var(--red);
}

/* Profile Content */
.profile-content {
  flex: 1;
  padding: var(--space-8);
  background: var(--white);
}

.profile-section {
  display: block;
}

.profile-section h2 {
  font-size: 1.8rem;
  color: var(--black);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-3);
  border-bottom: 2px solid var(--red);
  position: relative;
}

.profile-section h2::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--red);
  border-radius: 1px;
}

/* Section Spacing */
.profile-section h2.mt-4 {
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border);
}

/* Profile Form */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  background: var(--gray-light);
  padding: var(--space-6);
  border-radius: 12px;
  border: 1px solid var(--border);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--space-4);
}

.form-label {
  font-weight: 600;
  color: var(--black);
  margin-bottom: var(--space-2);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-label::before {
  content: '';
  width: 4px;
  height: 4px;
  background: var(--red);
  border-radius: 50%;
}

.form-control {
  padding: var(--space-4);
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
  background: var(--white);
  box-shadow: var(--shadow);
}

.form-control:focus {
  outline: none;
  border-color: var(--red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: var(--red);
}

.form-control.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-error {
  color: #dc2626;
  font-size: 0.8rem;
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-error::before {
  content: '⚠';
  font-size: 0.9rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border);
  justify-content: flex-end;
}

.btn-primary,
.btn-save {
  background: var(--red);
  color: var(--white);
  border: none;
  padding: var(--space-4) var(--space-8);
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow);
}

.btn-primary:hover,
.btn-save:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:disabled,
.btn-save:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-cancel {
  background: var(--white);
  color: var(--gray);
  border: 1px solid var(--border);
  padding: var(--space-4) var(--space-8);
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
  box-shadow: var(--shadow);
}

.btn-cancel:hover {
  background: var(--gray-light);
  border-color: var(--red);
  color: var(--red);
  transform: translateY(-1px);
}

/* Notification Settings */
.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border: 1px solid var(--border);
  border-radius: 6px;
  margin-bottom: var(--space-3);
}

.notification-info h4 {
  font-size: 1rem;
  color: var(--black);
  margin-bottom: var(--space-1);
}

.notification-info p {
  color: var(--gray);
  font-size: 0.9rem;
  margin: 0;
}

.notification-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.notification-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.notification-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-light);
  transition: 0.3s;
  border-radius: 24px;
}

.notification-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: var(--white);
  transition: 0.3s;
  border-radius: 50%;
}

.notification-toggle input:checked + .notification-slider {
  background-color: var(--red);
}

.notification-toggle input:checked + .notification-slider:before {
  transform: translateX(26px);
}

/* Order History */
.order-history {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.order-item {
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: var(--space-4);
  background: var(--white);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.order-number {
  font-weight: 600;
  color: var(--black);
}

.order-status {
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.completed {
  background: #f0fdf4;
  color: #166534;
}

.order-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.order-status.cancelled {
  background: #fef2f2;
  color: #dc2626;
}

.order-details {
  color: var(--gray);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    flex-direction: column;
  }
  
  .profile-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border);
  }
  
  .profile-nav {
    display: flex;
    overflow-x: auto;
    gap: var(--space-2);
  }
  
  .profile-nav-item {
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .notification-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
}
