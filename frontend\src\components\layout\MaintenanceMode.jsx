import { useSettings } from '../../contexts/SettingsContext';

const MaintenanceMode = () => {
  const { settings } = useSettings();

  return (
    <div className="maintenance-mode">
      <div className="maintenance-container">
        <h1>Site Under Maintenance</h1>
        <p>We're currently performing some scheduled maintenance on our website.</p>
        <p>Please check back soon. We apologize for any inconvenience.</p>
        
        {(settings.phone_number || settings.email) && (
          <div className="maintenance-contact">
            <h2>Contact Us</h2>
            {settings.phone_number && <p><strong>Phone:</strong> {settings.phone_number}</p>}
            {settings.email && <p><strong>Email:</strong> {settings.email}</p>}
          </div>
        )}
      </div>
    </div>
  );
};

export default MaintenanceMode;
