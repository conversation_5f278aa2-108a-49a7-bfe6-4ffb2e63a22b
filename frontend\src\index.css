:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  color-scheme: light;
  color: #1f2937;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #dc2626;
  text-decoration: none;
  transition: color 0.2s;
}
a:hover {
  color: #b91c1c;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 6px;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  background-color: #dc2626;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}
button:hover {
  background-color: #b91c1c;
  transform: translateY(-1px);
}
button:focus,
button:focus-visible {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}
