import axios from 'axios';

// Create an axios instance with default config
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api/',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Send cookies with requests
});

// Function to get CSRF token from the server
export const fetchCSRFToken = async () => {
  try {
    await api.get('csrf-token/');
  } catch (error) {
    console.error('Error fetching CSRF token:', error);
  }
};

// Function to get CSRF token from cookies
const getCSRFToken = () => {
  const name = 'csrftoken=';
  const decodedCookie = decodeURIComponent(document.cookie);
  const cookieArray = decodedCookie.split(';');

  for (let i = 0; i < cookieArray.length; i++) {
    let cookie = cookieArray[i].trim();
    if (cookie.indexOf(name) === 0) {
      return cookie.substring(name.length, cookie.length);
    }
  }
  return '';
};

// Add a request interceptor for any custom headers
api.interceptors.request.use(
  (config) => {
    // Add CSRF token to headers for non-GET requests
    if (config.method !== 'get') {
      const csrfToken = getCSRFToken();
      if (csrfToken) {
        config.headers['X-CSRFToken'] = csrfToken;
      }
    }

    // Add Authorization header with token if available
    const userInfoStr = localStorage.getItem('userInfo');
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        if (userInfo && userInfo.token) {
          config.headers['Authorization'] = `Token ${userInfo.token}`;
        }
      } catch (error) {
        console.error('Error parsing user info for token:', error);
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle common errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      // Clear local storage and redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('auth/login/', credentials),
  register: (userData) => api.post('auth/register/', userData),
  logout: () => api.post('auth/logout/'),
  getProfile: () => api.get('users/me/'),
  updateProfile: (data) => api.put('profiles/me/', data),
  changePassword: (data) => api.post('auth/change-password/', data),
};

// Products API
export const productsAPI = {
  getAll: (params) => api.get('products/', { params }),
  getById: (id) => api.get(`products/${id}/`),
  getByCategory: (categoryId, params) =>
    api.get('products/', { params: { category: categoryId, ...params } }),
  getFeatured: () => api.get('products/', { params: { is_featured: true } }),
  search: (query) => api.get('products/', { params: { search: query } }),
};

// Categories API
export const categoriesAPI = {
  getAll: () => api.get('categories/'),
  getById: (id) => api.get(`categories/${id}/`),
};

// Packs API
export const packsAPI = {
  getAll: (params = {}) => {
    console.log('🎁 Fetching packs with params:', params);
    return api.get('packs/', { params })
      .then(response => {
        console.log('✅ Packs fetched successfully:', response.data);
        return response;
      })
      .catch(error => {
        console.error('❌ Error fetching packs:', error);
        throw error;
      });
  },
  getById: (id) => {
    console.log('🎁 Fetching pack by ID:', id);
    return api.get(`packs/${id}/`)
      .then(response => {
        console.log('✅ Pack fetched successfully:', response.data);
        return response;
      })
      .catch(error => {
        console.error('❌ Error fetching pack:', error);
        throw error;
      });
  },
  getFeatured: () => {
    console.log('🎁 Fetching featured packs');
    return api.get('packs/', { params: { is_featured: true } })
      .then(response => {
        console.log('✅ Featured packs fetched successfully:', response.data);
        return response;
      })
      .catch(error => {
        console.error('❌ Error fetching featured packs:', error);
        throw error;
      });
  },
  create: (data) => api.post('packs/', data),
  update: (id, data) => api.put(`packs/${id}/`, data),
  delete: (id) => api.delete(`packs/${id}/`)
};

// Import guest cart utilities
import {
  getGuestCart,
  addToGuestCart,
  updateGuestCartQuantity,
  removeFromGuestCart,
  clearGuestCart,
  isLocalStorageAvailable
} from '../utils/guestCart';

// Helper function to check if user is authenticated
const isUserAuthenticated = () => {
  // Check for userInfo in localStorage (which contains the token)
  const userInfo = localStorage.getItem('userInfo');
  console.log('🔐 isUserAuthenticated check - userInfo:', userInfo ? 'exists' : 'null');

  if (!userInfo) return false;

  try {
    const parsed = JSON.parse(userInfo);
    const isAuth = !!(parsed && parsed.token);
    console.log('🔐 isUserAuthenticated result:', isAuth);
    return isAuth;
  } catch (error) {
    console.error('Error parsing userInfo from localStorage:', error);
    return false;
  }
};

// Cart API
export const cartAPI = {
  getCart: () => {
    console.log('📦 cartAPI.getCart() called');

    // If user is not authenticated, return guest cart
    if (!isUserAuthenticated()) {
      console.log('👤 User not authenticated, returning guest cart');
      return Promise.resolve({ data: getGuestCart() });
    }

    console.log('🔐 User authenticated, fetching server cart');
    return api.get('cart/')
      .then(response => {
        console.log('✅ Server cart fetched successfully:', response.data);

        // Handle both paginated and direct cart response formats
        let cartData = response.data;

        // If response is paginated (has 'results' array), extract the first cart
        if (cartData.results && Array.isArray(cartData.results)) {
          console.log('📦 Detected paginated response, extracting cart from results');
          cartData = cartData.results.length > 0 ? cartData.results[0] : { items: [], total: 0 };
        }

        // Ensure cartData has the expected structure
        if (!cartData.items) {
          cartData.items = [];
        }
        if (typeof cartData.total === 'undefined') {
          cartData.total = 0;
        }

        console.log('✅ Processed cart data:', cartData);
        console.log('✅ Server cart items:', cartData?.items?.length || 0, 'items');
        console.log('✅ Server cart total:', cartData?.total || 0);

        // Return the processed cart data
        return { ...response, data: cartData };
      })
      .catch(error => {
        console.error('❌ Error fetching server cart:', error);
        if (error.response) {
          console.error('Cart API error:', error.response.status, error.response.data);
        }
        throw error;
      });
  },
  addToCart: (product, quantity = 1) => {
    console.log('API addToCart called with:', { product, quantity });

    // Validate product
    if (!product) {
      console.error('Product is null or undefined');
      return Promise.reject(new Error('Product is required'));
    }

    // Validate product.id
    if (!product.id) {
      console.error('Product ID is missing');
      return Promise.reject(new Error('Product ID is required'));
    }

    // If user is not authenticated, use guest cart
    if (!isUserAuthenticated()) {
      console.log('🚫 User not authenticated, adding to guest cart');
      try {
        const updatedCart = addToGuestCart(product, quantity);
        return Promise.resolve({ data: updatedCart });
      } catch (error) {
        console.error('Error adding to guest cart:', error);
        return Promise.reject(error);
      }
    }

    console.log('✅ User authenticated, adding to server cart');

    // Ensure product.id is a number
    let productId;
    try {
      productId = parseInt(product.id, 10);
      if (isNaN(productId)) {
        throw new Error('Not a number');
      }
    } catch (error) {
      console.error('Invalid product ID:', product.id, error);
      return Promise.reject(new Error('Invalid product ID'));
    }

    // Ensure quantity is a number
    let qty;
    try {
      qty = parseInt(quantity, 10);
      if (isNaN(qty) || qty <= 0) {
        throw new Error('Invalid quantity value');
      }
    } catch (error) {
      console.error('Invalid quantity:', quantity, error);
      return Promise.reject(new Error('Invalid quantity'));
    }

    console.log('Sending request with:', { product_id: productId, quantity: qty });

    // Make the API call with error handling
    return api.post('cart/add_item/', {
      product_id: productId,
      quantity: qty
    }).catch(error => {
      console.error('Error in addToCart API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    });
  },

  addPackToCart: (pack, quantity = 1) => {
    console.log('API addPackToCart called with:', { pack, quantity });

    // Validate pack
    if (!pack) {
      console.error('Pack is null or undefined');
      return Promise.reject(new Error('Pack is required'));
    }

    // Validate pack.id
    if (!pack.id) {
      console.error('Pack ID is missing');
      return Promise.reject(new Error('Pack ID is required'));
    }

    // If user is not authenticated, use guest cart
    if (!isUserAuthenticated()) {
      console.log('🚫 User not authenticated, adding pack to guest cart');
      try {
        const guestCart = getGuestCart();

        // Check if pack already exists
        const existingPackIndex = guestCart.items.findIndex(item =>
          item.type === 'pack' && item.pack && item.pack.id === pack.id
        );

        if (existingPackIndex >= 0) {
          // Update pack quantity
          guestCart.items[existingPackIndex].quantity += quantity;
        } else {
          // Add new pack
          guestCart.items.push({
            id: Date.now(),
            pack: pack,
            quantity: quantity,
            type: 'pack'
          });
        }

        // Recalculate total
        guestCart.total = guestCart.items.reduce((sum, item) => {
          if (item.type === 'pack' && item.pack) {
            return sum + (parseFloat(item.pack.pack_price) * item.quantity);
          } else if (item.product) {
            return sum + (parseFloat(item.product.price) * item.quantity);
          }
          return sum;
        }, 0);

        // Save to localStorage
        if (isLocalStorageAvailable()) {
          localStorage.setItem('nordica_guest_cart', JSON.stringify(guestCart));
        }

        return Promise.resolve({ data: guestCart });
      } catch (error) {
        console.error('Error adding pack to guest cart:', error);
        return Promise.reject(error);
      }
    }

    // User is authenticated, use server cart
    console.log('✅ User authenticated, adding pack to server cart');

    // Ensure pack.id is a number
    let packId;
    try {
      packId = parseInt(pack.id, 10);
      if (isNaN(packId)) {
        throw new Error('Not a number');
      }
    } catch (error) {
      console.error('Invalid pack ID:', pack.id, error);
      return Promise.reject(new Error('Invalid pack ID'));
    }

    // Ensure quantity is a number
    let qty;
    try {
      qty = parseInt(quantity, 10);
      if (isNaN(qty) || qty <= 0) {
        throw new Error('Invalid quantity value');
      }
    } catch (error) {
      console.error('Invalid quantity:', quantity, error);
      return Promise.reject(new Error('Invalid quantity'));
    }

    console.log('Sending pack request with:', { pack_id: packId, quantity: qty });

    // Make the API call with error handling
    return api.post('cart/add_item/', {
      pack_id: packId,
      quantity: qty
    }).catch(error => {
      console.error('Error in addPackToCart API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    });
  },

  updateQuantity: (productId, quantity) => {
    console.log('Updating quantity:', { productId, quantity });

    // Validate productId
    if (!productId) {
      console.error('Product ID is missing');
      return Promise.reject(new Error('Product ID is required'));
    }

    // Validate quantity
    if (quantity === undefined || quantity === null || isNaN(parseInt(quantity, 10))) {
      console.error('Invalid quantity:', quantity);
      return Promise.reject(new Error('Invalid quantity'));
    }

    // If user is not authenticated, use guest cart
    if (!isUserAuthenticated()) {
      console.log('User not authenticated, updating guest cart quantity');
      try {
        const updatedCart = updateGuestCartQuantity(productId, quantity);
        return Promise.resolve({ data: updatedCart });
      } catch (error) {
        console.error('Error updating guest cart quantity:', error);
        return Promise.reject(error);
      }
    }

    return api.post('cart/update_quantity/', {
      product_id: productId,
      quantity: parseInt(quantity, 10)
    }).catch(error => {
      console.error('Error in updateQuantity API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    });
  },

  updateItemQuantity: (itemData, quantity) => {
    console.log('Updating item quantity:', { itemData, quantity });

    // Validate that either product_id or pack_id is provided
    if (!itemData.product_id && !itemData.pack_id) {
      console.error('Either product_id or pack_id is required');
      return Promise.reject(new Error('Either product_id or pack_id is required'));
    }

    if (itemData.product_id && itemData.pack_id) {
      console.error('Cannot specify both product_id and pack_id');
      return Promise.reject(new Error('Cannot specify both product_id and pack_id'));
    }

    // Validate quantity
    if (quantity === undefined || quantity === null || isNaN(parseInt(quantity, 10))) {
      console.error('Invalid quantity:', quantity);
      return Promise.reject(new Error('Invalid quantity'));
    }

    // If user is not authenticated, use guest cart
    if (!isUserAuthenticated()) {
      console.log('User not authenticated, updating guest cart quantity');
      try {
        if (itemData.product_id) {
          const updatedCart = updateGuestCartQuantity(itemData.product_id, quantity);
          return Promise.resolve({ data: updatedCart });
        } else if (itemData.pack_id) {
          // Handle pack quantity update in guest cart
          const guestCart = getGuestCart();
          const packIndex = guestCart.items.findIndex(item =>
            item.type === 'pack' && item.pack && item.pack.id === itemData.pack_id
          );

          if (packIndex >= 0) {
            if (parseInt(quantity, 10) <= 0) {
              // Remove pack if quantity is 0 or less
              guestCart.items.splice(packIndex, 1);
            } else {
              // Update pack quantity
              guestCart.items[packIndex].quantity = parseInt(quantity, 10);
            }

            // Recalculate total
            guestCart.total = guestCart.items.reduce((sum, item) => {
              if (item.type === 'pack' && item.pack) {
                return sum + (parseFloat(item.pack.pack_price) * item.quantity);
              } else if (item.product) {
                return sum + (parseFloat(item.product.price) * item.quantity);
              }
              return sum;
            }, 0);

            // Save to localStorage
            if (isLocalStorageAvailable()) {
              localStorage.setItem('nordica_guest_cart', JSON.stringify(guestCart));
            }
          }

          return Promise.resolve({ data: guestCart });
        }
      } catch (error) {
        console.error('Error updating guest cart quantity:', error);
        return Promise.reject(error);
      }
    }

    // User is authenticated, use server cart
    console.log('User authenticated, updating server cart quantity');
    return api.post('cart/update_quantity/', {
      ...itemData,
      quantity: parseInt(quantity, 10)
    }).catch(error => {
      console.error('Error in updateItemQuantity API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    });
  },

  removeFromCart: (productId) => {
    console.log('Removing from cart:', productId);

    // Validate productId
    if (!productId) {
      console.error('Product ID is missing');
      return Promise.reject(new Error('Product ID is required'));
    }

    // If user is not authenticated, use guest cart
    if (!isUserAuthenticated()) {
      console.log('User not authenticated, removing from guest cart');
      try {
        const updatedCart = removeFromGuestCart(productId);
        return Promise.resolve({ data: updatedCart });
      } catch (error) {
        console.error('Error removing from guest cart:', error);
        return Promise.reject(error);
      }
    }

    return api.post('cart/remove_item/', {
      product_id: productId
    }).catch(error => {
      console.error('Error in removeFromCart API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    });
  },

  removeItem: (itemData) => {
    console.log('Removing item from cart:', itemData);

    // Validate that either product_id or pack_id is provided
    if (!itemData.product_id && !itemData.pack_id) {
      console.error('Either product_id or pack_id is required');
      return Promise.reject(new Error('Either product_id or pack_id is required'));
    }

    if (itemData.product_id && itemData.pack_id) {
      console.error('Cannot specify both product_id and pack_id');
      return Promise.reject(new Error('Cannot specify both product_id and pack_id'));
    }

    // If user is not authenticated, use guest cart
    if (!isUserAuthenticated()) {
      console.log('User not authenticated, removing from guest cart');
      try {
        if (itemData.product_id) {
          const updatedCart = removeFromGuestCart(itemData.product_id);
          return Promise.resolve({ data: updatedCart });
        } else if (itemData.pack_id) {
          // Handle pack removal from guest cart
          const guestCart = getGuestCart();
          guestCart.items = guestCart.items.filter(item =>
            !(item.type === 'pack' && item.pack && item.pack.id === itemData.pack_id)
          );

          // Recalculate total
          guestCart.total = guestCart.items.reduce((sum, item) => {
            if (item.type === 'pack' && item.pack) {
              return sum + (parseFloat(item.pack.pack_price) * item.quantity);
            } else if (item.product) {
              return sum + (parseFloat(item.product.price) * item.quantity);
            }
            return sum;
          }, 0);

          // Save to localStorage
          if (isLocalStorageAvailable()) {
            localStorage.setItem('nordica_guest_cart', JSON.stringify(guestCart));
          }

          return Promise.resolve({ data: guestCart });
        }
      } catch (error) {
        console.error('Error removing from guest cart:', error);
        return Promise.reject(error);
      }
    }

    // User is authenticated, use server cart
    console.log('User authenticated, removing from server cart');
    console.log('📤 Sending remove request with data:', itemData);
    return api.post('cart/remove_item/', itemData).catch(error => {
      console.error('Error in removeItem API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    });
  },

  clearCart: () => {
    console.log('Clearing cart');

    // If user is not authenticated, clear guest cart
    if (!isUserAuthenticated()) {
      console.log('User not authenticated, clearing guest cart');
      try {
        clearGuestCart();
        return Promise.resolve({ data: { items: [], total: 0 } });
      } catch (error) {
        console.error('Error clearing guest cart:', error);
        return Promise.reject(error);
      }
    }

    return api.post('cart/clear/')
      .catch(error => {
        console.error('Error in clearCart API call:', error);
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }
        throw error;
      });
  },
};



// Orders API
export const ordersAPI = {
  getAll: (params) => api.get('orders/', { params }),
  getById: (id) => api.get(`orders/${id}/`),
  create: (orderData) => api.post('orders/', orderData),
  updateStatus: (id, statusData) => api.patch(`orders/${id}/`, statusData),
  deleteOrderItem: (orderId, itemId) => api.delete(`orders/${orderId}/items/${itemId}/`),
};

// Settings API
export const settingsAPI = {
  getSettings: () => api.get('settings/current/'),
  updateSettings: (data) => api.put('settings/1/', data),
};

export default api;
