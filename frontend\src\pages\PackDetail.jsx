import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { packsAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';
import { FaArrowLeft, FaShoppingCart, FaSpinner, FaGift, FaTag, FaCheck, FaTimes } from 'react-icons/fa';
import '../styles/pack-detail.css';

const PackDetail = () => {
  const { packId } = useParams();
  const { addPackToCart } = useCart();
  const [pack, setPack] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [addingToCart, setAddingToCart] = useState(false);

  useEffect(() => {
    const fetchPack = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await packsAPI.getById(packId);
        setPack(response.data);
      } catch (err) {
        setError('Failed to load pack details');
        console.error('Error fetching pack:', err);
      } finally {
        setLoading(false);
      }
    };

    if (packId) {
      fetchPack();
    }
  }, [packId]);

  const handleAddToCart = async () => {
    if (!pack) return;

    setAddingToCart(true);
    try {
      // Add pack to cart
      await addPackToCart(pack, 1);
    } catch (error) {
      console.error('Error adding pack to cart:', error);
    } finally {
      setAddingToCart(false);
    }
  };

  if (loading) {
    return (
      <div className="pack-detail-page">
        <div className="loading-container">
          <FaSpinner className="loading-spinner" />
          <h2>Loading Pack Details...</h2>
          <p>Please wait while we fetch the pack information</p>
        </div>
      </div>
    );
  }

  if (error || !pack) {
    return (
      <div className="pack-detail-page">
        <div className="error-container">
          <FaTimes size={40} />
          <h2>Pack Not Found</h2>
          <p>{error || 'The pack you are looking for does not exist.'}</p>
          <Link to="/packs" className="btn btn-primary">
            <FaArrowLeft /> Back to Packs
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="pack-detail-page">
      {/* Back Button */}
      <div className="pack-detail-header">
        <Link to="/packs" className="back-button">
          <FaArrowLeft /> Back to Packs
        </Link>
      </div>

      <div className="pack-detail-container">
        {/* Pack Image */}
        <div className="pack-image-section">
          <div className="pack-image-container">
            {pack.image ? (
              <img 
                src={pack.image} 
                alt={pack.name}
                className="pack-image"
              />
            ) : (
              <div className="pack-image-placeholder">
                <FaGift size={80} />
              </div>
            )}
            
            {/* Discount Badge */}
            {pack.discount_percentage > 0 && (
              <div className="pack-discount-badge">
                <FaTag />
                {Math.round(pack.discount_percentage)}% OFF
              </div>
            )}
          </div>
        </div>

        {/* Pack Info */}
        <div className="pack-info-section">
          <div className="pack-header">
            <h1 className="pack-name">{pack.name}</h1>
            <p className="pack-description">{pack.description}</p>
          </div>

          {/* Pricing */}
          <div className="pack-pricing-detail">
            <div className="pack-prices">
              <span className="pack-original-price">{pack.original_price} TND</span>
              <span className="pack-current-price">{pack.pack_price} TND</span>
            </div>
            <div className="pack-savings">
              <FaTag />
              You save: {pack.savings} TND ({Math.round(pack.discount_percentage)}% OFF)
            </div>
          </div>

          {/* Products in Pack */}
          <div className="pack-products-detail">
            <h3>What's Included in This Pack</h3>
            <div className="pack-products-list">
              {pack.pack_products?.map((packProduct, index) => (
                <Link
                  key={index}
                  to={`/products/${packProduct.product.id}`}
                  className="pack-product-item clickable"
                >
                  <div className="pack-product-info">
                    <FaCheck className="check-icon" />
                    <div className="product-details">
                      <h4>{packProduct.product.name}</h4>
                      <p className="product-category">{packProduct.product.category_name}</p>
                      <p className="product-price">{packProduct.product.price} TND each</p>
                    </div>
                  </div>
                  <div className="product-quantity">
                    <span className="quantity-badge">x{packProduct.quantity}</span>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Add to Cart */}
          <div className="pack-actions">
            <button
              onClick={handleAddToCart}
              disabled={addingToCart}
              className="pack-add-to-cart-btn-detail"
            >
              {addingToCart ? (
                <>
                  <FaSpinner className="spinning" />
                  Adding to Cart...
                </>
              ) : (
                <>
                  <FaShoppingCart />
                  Add Pack to Cart
                </>
              )}
            </button>
          </div>

          {/* Pack Benefits */}
          <div className="pack-benefits">
            <h3>Why Choose This Pack?</h3>
            <ul className="benefits-list">
              <li><FaCheck /> Save {pack.savings} TND compared to buying individually</li>
              <li><FaCheck /> Carefully curated products that work well together</li>
              <li><FaCheck /> Free delivery on orders over 100 TND</li>
              <li><FaCheck /> Cash on delivery available</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackDetail;
