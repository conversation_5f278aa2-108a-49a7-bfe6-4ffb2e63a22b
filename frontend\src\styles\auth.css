/* AUTH PAGES - CLEAN & SIMPLE */

/* Auth Page Container */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--gray-light) 0%, var(--white) 100%);
  padding: var(--space-4);
}

/* Auth Container */
.auth-container {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 450px;
}

/* Auth Header */
.auth-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.auth-logo {
  font-size: 2rem;
  font-weight: 700;
  color: var(--red);
  margin-bottom: var(--space-4);
  letter-spacing: 1px;
}

.auth-title {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: var(--space-2);
  font-weight: 600;
}

.auth-subtitle {
  color: var(--gray);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

/* Auth Form */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-weight: 600;
  color: var(--black);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

.form-control {
  padding: var(--space-3);
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
  background: var(--white);
}

.form-control:focus {
  outline: none;
  border-color: var(--red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-control.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-control::placeholder {
  color: var(--gray);
}

/* Password Toggle */
.password-toggle {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: 4px;
  transition: color 0.2s;
}

.password-toggle:hover {
  color: var(--red);
}

/* Form Error */
.form-error {
  color: #dc2626;
  font-size: 0.8rem;
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-error::before {
  content: '⚠';
  font-size: 0.9rem;
}

/* Error Container */
.error-container {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: var(--space-3);
  margin-bottom: var(--space-4);
}

.error-container p {
  color: #dc2626;
  margin: 0;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.error-container p::before {
  content: '❌';
  font-size: 1rem;
}

/* Info Container */
.info-container {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: var(--space-3);
  margin-bottom: var(--space-4);
}

.info-container p {
  color: #1d4ed8;
  margin: 0;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.info-container p::before {
  content: 'ℹ️';
  font-size: 1rem;
}

/* Success Container */
.success-container {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: var(--space-3);
  margin-bottom: var(--space-4);
}

.success-container p {
  color: #166534;
  margin: 0;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.success-container p::before {
  content: '✅';
  font-size: 1rem;
}

/* Forgot Password */
.forgot-password {
  text-align: right;
  margin-top: var(--space-2);
}

.forgot-password a {
  color: var(--red);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.forgot-password a:hover {
  text-decoration: underline;
}

/* Form Check */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: var(--space-2) 0;
}

.form-check input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--red);
  cursor: pointer;
}

.form-check label {
  color: var(--gray);
  font-size: 0.9rem;
  cursor: pointer;
  user-select: none;
}

/* Auth Button */
.auth-form .btn {
  padding: var(--space-4);
  font-size: 1rem;
  font-weight: 600;
  margin-top: var(--space-2);
  border-radius: 8px;
  transition: all 0.2s;
}

.auth-form .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Auth Footer */
.auth-footer {
  text-align: center;
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border);
}

.auth-footer p {
  color: var(--gray);
  margin: 0;
  font-size: 0.9rem;
}

.auth-footer a {
  color: var(--red);
  text-decoration: none;
  font-weight: 600;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* Form Row (for Register page) */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

/* Terms and Conditions */
.terms-check {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  margin: var(--space-4) 0;
}

.terms-check input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--red);
  cursor: pointer;
  margin-top: 2px;
  flex-shrink: 0;
}

.terms-check label {
  color: var(--gray);
  font-size: 0.9rem;
  cursor: pointer;
  user-select: none;
  line-height: 1.5;
}

.terms-check a {
  color: var(--red);
  text-decoration: none;
}

.terms-check a:hover {
  text-decoration: underline;
}

/* Loading State */
.auth-form .btn[disabled] {
  position: relative;
}

.auth-form .btn[disabled]::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--white);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-page {
    padding: var(--space-2);
    align-items: flex-start;
    padding-top: var(--space-8);
  }
  
  .auth-container {
    padding: var(--space-6);
    max-width: 100%;
  }
  
  .auth-title {
    font-size: 1.5rem;
  }
  
  .auth-logo {
    font-size: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: var(--space-4);
    border-radius: 8px;
  }
  
  .auth-header {
    margin-bottom: var(--space-6);
  }
}
