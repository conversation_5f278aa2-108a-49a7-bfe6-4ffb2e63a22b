/* SEARCH PAGE - ORGANIZED & CLEAN */

/* Page Container */
.search-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-4);
  min-height: 100vh;
}

/* Header Section */
.search-page-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  margin-bottom: var(--space-8);
}

.search-page-header h1 {
  font-size: 2.5rem;
  color: var(--white);
  margin-bottom: var(--space-3);
  font-weight: 700;
}

.search-page-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Search Form Section */
.search-page-form {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  align-items: stretch;
  background: var(--white);
  padding: var(--space-4);
  border-radius: 8px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.search-input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-page-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s;
  background: var(--white);
}

.search-page-input:focus {
  outline: none;
  border-color: var(--red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.search-page-input::placeholder {
  color: var(--gray);
}

.clear-search-btn {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: 4px;
  transition: color 0.2s;
}

.clear-search-btn:hover {
  color: var(--red);
}

.search-page-button {
  padding: var(--space-3) var(--space-4);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  white-space: nowrap;
}

.search-page-button:hover {
  background: var(--red-dark);
}

.search-page-button svg {
  font-size: 1rem;
}



/* Search Results */
.search-page-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

/* Use Product List Card Styles for Search Results */
.search-page-results .product-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-page-results .product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--red);
}

.search-page-results .card-img-container {
  position: relative;
  overflow: hidden;
  display: block;
  text-decoration: none;
  height: 200px;
  background: var(--gray-light);
}

.search-page-results .card-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.search-page-results .card-img-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-light);
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.search-page-results .product-card:hover .card-img {
  transform: scale(1.05);
}

.search-page-results .card-body {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  flex: 1;
}

.search-page-results .card-title-link {
  text-decoration: none;
  margin-bottom: var(--space-2);
}

.search-page-results .card-title {
  font-size: 1.1rem;
  color: var(--black);
  margin: 0;
  font-weight: 600;
  line-height: 1.3;
  transition: color 0.2s ease;
}

.search-page-results .card-title-link:hover .card-title {
  color: var(--red);
}

.search-page-results .card-text {
  margin: 0 0 var(--space-3) 0;
  font-size: 0.9rem;
}

.search-page-results .card-text a {
  color: var(--gray);
  text-decoration: none;
  transition: color 0.2s ease;
}

.search-page-results .card-text a:hover {
  color: var(--red);
}

.search-page-results .card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-3) var(--space-4);
  background: linear-gradient(135deg, #fafafa, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.search-page-results .card-meta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--red), #f59e0b, var(--red));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

.search-page-results .card-price {
  font-size: 1.6rem;
  font-weight: 900;
  color: var(--red);
  margin: 0;
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
  position: relative;
}

.search-page-results .card-price::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--red), transparent);
  border-radius: 1px;
}

.search-page-results .card-price::after {
  content: 'DT';
  font-size: 1rem;
  font-weight: 700;
  color: var(--gray);
  background: var(--white);
  padding: var(--space-1) var(--space-2);
  border-radius: 6px;
  border: 1px solid var(--border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-page-results .card-status {
  padding: var(--space-2) var(--space-4);
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-page-results .card-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  box-shadow: 0 0 8px currentColor;
  animation: pulse 2s infinite;
}

.search-page-results .card-status::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.search-page-results .card-status:hover::after {
  left: 100%;
}

.search-page-results .card-status.in-stock {
  background: linear-gradient(135deg, #10b981, #059669);
  color: var(--white);
  border: 1px solid #047857;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.search-page-results .card-status.in-stock::before {
  background: #34d399;
  box-shadow: 0 0 12px #34d399;
}

.search-page-results .card-status.out-of-stock {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: var(--white);
  border: 1px solid #b91c1c;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.search-page-results .card-status.out-of-stock::before {
  background: #f87171;
  box-shadow: 0 0 12px #f87171;
}

.search-page-results .card-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: auto;
}

.search-page-results .card-actions .btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  text-decoration: none;
  border: none;
}

.search-page-results .btn-outline {
  background: var(--white);
  color: var(--gray);
  border: 1px solid var(--border);
}

.search-page-results .btn-outline:hover {
  background: var(--gray-light);
  border-color: var(--red);
  color: var(--red);
}

.search-page-results .btn-primary {
  background: var(--red);
  color: var(--white);
  border: 1px solid var(--red);
}

.search-page-results .btn-primary:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
}

.search-page-results .btn-primary:disabled {
  background: var(--gray);
  border-color: var(--gray);
  cursor: not-allowed;
  transform: none;
}

.search-page-results .btn-disabled {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
  color: var(--white);
  border: 1px solid #6b7280;
  cursor: not-allowed;
  opacity: 0.7;
}

.search-page-results .btn-disabled:hover {
  background: linear-gradient(135deg, #6b7280, #9ca3af);
  transform: none;
  box-shadow: none;
}

.search-page-results .btn svg {
  font-size: 0.9rem;
}

.search-page-results .loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* No Results */
.search-page-no-results {
  text-align: center;
  padding: var(--space-12);
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: var(--shadow);
  max-width: 600px;
  margin: 0 auto;
}

.no-results-icon {
  font-size: 4rem;
  color: var(--gray);
  margin-bottom: var(--space-4);
}

.search-page-no-results h3 {
  font-size: 1.8rem;
  color: var(--black);
  margin-bottom: var(--space-3);
}

.search-page-no-results p {
  color: var(--gray);
  font-size: 1.1rem;
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.search-page-no-results p:last-of-type {
  margin-bottom: var(--space-6);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-light);
  border-top: 4px solid var(--red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: var(--gray);
  font-size: 1.1rem;
}

/* Search Suggestions */
.search-suggestions {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  margin-bottom: var(--space-8);
  box-shadow: var(--shadow);
}

.search-suggestions h3 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-4);
}

.search-suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.search-suggestion {
  padding: var(--space-2) var(--space-3);
  background: var(--gray-light);
  border: 1px solid var(--border);
  border-radius: 6px;
  color: var(--gray);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.search-suggestion:hover {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
}

/* Search Filters */
.search-filters {
  display: flex;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--white), #fafafa);
  padding: var(--space-6);
  border-radius: 20px;
  border: 2px solid var(--border);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
}

.search-filters::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--red), #f59e0b, var(--red));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.search-filter {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  min-width: 200px;
}

.search-filter label {
  font-weight: 700;
  color: var(--black);
  font-size: 1rem;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  min-width: 120px;
  position: relative;
  padding: var(--space-2) 0;
}

.search-filter label::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--red);
  border-radius: 1px;
}

.search-filter label svg {
  color: var(--red);
  font-size: 1.2rem;
  filter: drop-shadow(0 1px 2px rgba(220, 38, 38, 0.2));
}

.search-filter select {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--border);
  border-radius: 12px;
  font-size: 1rem;
  background: var(--white);
  color: var(--black);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23dc2626' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 1rem center;
  background-repeat: no-repeat;
  background-size: 1.2em 1.2em;
  padding-right: 3.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.search-filter select:hover {
  border-color: var(--red);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
  background-color: rgba(220, 38, 38, 0.02);
}

.search-filter select:focus {
  outline: none;
  border-color: var(--red);
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1), 0 8px 25px rgba(220, 38, 38, 0.15);
  transform: translateY(-2px);
}

.search-filter select option {
  padding: var(--space-2);
  font-weight: 500;
  color: var(--black);
  background: var(--white);
}

.search-filter select option:hover {
  background: var(--gray-light);
}

/* Filter Actions */
.search-filter-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.clear-filters-btn {
  padding: var(--space-3) var(--space-5);
  background: linear-gradient(135deg, var(--gray-light), #f8f9fa);
  color: var(--gray);
  border: 2px solid var(--border);
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.clear-filters-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.clear-filters-btn:hover::before {
  left: 100%;
}

.clear-filters-btn:hover {
  background: linear-gradient(135deg, var(--red), var(--red-dark));
  color: var(--white);
  border-color: var(--red);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.clear-filters-btn svg {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.clear-filters-btn:hover svg {
  transform: rotate(180deg);
}

/* Welcome State */
.search-welcome {
  text-align: center;
  padding: var(--space-12);
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: var(--shadow);
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  font-size: 4rem;
  color: var(--gray);
  margin-bottom: var(--space-4);
}

.search-welcome h3 {
  font-size: 1.8rem;
  color: var(--black);
  margin-bottom: var(--space-3);
}

.search-welcome p {
  color: var(--gray);
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.6;
}

/* Results Count */
.search-results-count {
  margin-bottom: var(--space-4);
  color: var(--gray);
  font-size: 0.9rem;
  text-align: center;
}

.search-results-count strong {
  color: var(--black);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .search-page-form {
    max-width: 100%;
  }

  .search-filters {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .search-page {
    padding: var(--space-4) var(--space-2);
  }

  .search-page-header h1 {
    font-size: 2rem;
  }

  .search-page-header p {
    font-size: 1rem;
  }

  .search-page-form {
    flex-direction: column;
    max-width: 100%;
    gap: var(--space-3);
  }

  .search-page-button {
    justify-content: center;
    padding: var(--space-3) var(--space-4);
  }

  .search-page-results {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-4);
  }

  .search-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
    padding: var(--space-4);
  }

  .search-filter {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
    min-width: auto;
  }

  .search-filter label {
    min-width: auto;
    font-size: 0.95rem;
  }

  .search-filter select {
    width: 100%;
    padding-right: 2.5rem;
  }

  .search-filter-actions {
    margin-top: var(--space-2);
    width: 100%;
  }

  .clear-filters-btn {
    width: 100%;
    justify-content: center;
    padding: var(--space-3);
  }

  .search-filter {
    justify-content: space-between;
    min-width: auto;
  }

  .search-filter label {
    min-width: 80px;
  }

  .search-suggestions {
    margin-bottom: var(--space-6);
  }
}

@media (max-width: 480px) {
  .search-page {
    padding: var(--space-2);
  }

  .search-page-header h1 {
    font-size: 1.8rem;
  }

  .search-page-input {
    padding: var(--space-3);
    font-size: 0.9rem;
  }

  .search-page-button {
    padding: var(--space-3);
    font-size: 0.9rem;
  }

  .search-page-results {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .search-suggestions-list {
    flex-direction: column;
    gap: var(--space-2);
  }

  .suggestion-item {
    padding: var(--space-2);
    font-size: 0.85rem;
  }

  .search-suggestion {
    text-align: center;
  }

  .search-page-no-results,
  .search-welcome {
    padding: var(--space-4);
  }

  .search-page-no-results h3,
  .search-welcome h3 {
    font-size: 1.3rem;
  }
}
