import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ordersAPI } from '../../services/api';
import {
  FaArrowLeft,
  FaTrash,
  FaCheck,
  FaTimes,
  FaPrint,
  FaDownload,
  FaExclamationTriangle,
  FaShippingFast,
  FaBoxOpen,
  FaCheckCircle
} from 'react-icons/fa';
import { useReactToPrint } from 'react-to-print';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import '../../styles/admin-order-detail.css';

const AdminOrderDetail = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const printRef = useRef();

  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteItemId, setDeleteItemId] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);

  // Status options for dropdown
  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await ordersAPI.getById(orderId);
        setOrder(response.data);

      } catch (err) {
        console.error('Error fetching order:', err);
        setError('Failed to load order details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [orderId]);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle print
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `Order_${orderId}`,
    onAfterPrint: () => console.log('Print completed')
  });

  // Handle download as PDF
  const handleDownloadPDF = () => {
    if (!order) return;

    try {
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(20);
      doc.text(`Order #${order.id}`, 14, 22);

      // Add date
      doc.setFontSize(12);
      doc.text(`Date: ${formatDate(order.created_at)}`, 14, 32);

      // Add status
      doc.text(`Status: ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}`, 14, 40);

      // Add customer info
      doc.setFontSize(16);
      doc.text('Customer Information', 14, 55);
      doc.setFontSize(12);
      doc.text(`Name: ${order.full_name}`, 14, 65);
      doc.text(`Email: ${order.email}`, 14, 73);
      doc.text(`Phone: ${order.phone}`, 14, 81);
      doc.text(`Address: ${order.address}`, 14, 89);
      doc.text(`City: ${order.city}`, 14, 97);
      doc.text(`State: ${order.state}`, 14, 105);

      // Add order items
      doc.setFontSize(16);
      doc.text('Order Items', 14, 120);

      // Create table for order items
      const tableColumn = ["Product", "Price", "Quantity", "Total"];
      const tableRows = [];

      order.items.forEach(item => {
        const itemData = [
          item.product.name,
          `${item.price} DT`,
          item.quantity,
          `${(item.price * item.quantity).toFixed(2)} DT`
        ];
        tableRows.push(itemData);
      });

      doc.autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 125,
        theme: 'grid',
        styles: { fontSize: 10 }
      });

      // Add total
      const finalY = doc.lastAutoTable.finalY + 10;
      doc.setFontSize(14);
      doc.text(`Total: ${order.total_price} DT`, 150, finalY, { align: 'right' });

      // Save the PDF
      doc.save(`Order_${order.id}.pdf`);
    } catch (err) {
      console.error('Error generating PDF:', err);
      alert('Failed to generate PDF');
    }
  };

  // Handle status update
  const handleStatusUpdate = async (newStatus) => {
    try {
      setStatusUpdateLoading(true);
      await ordersAPI.updateStatus(orderId, { status: newStatus });

      // Update order in state
      setOrder(prevOrder => ({ ...prevOrder, status: newStatus }));

      // Show success message
      setStatusUpdateSuccess(true);
      setTimeout(() => setStatusUpdateSuccess(false), 3000);

    } catch (err) {
      console.error('Error updating order status:', err);
      alert('Failed to update order status');
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <FaExclamationTriangle className="status-icon pending" />;
      case 'processing':
        return <FaBoxOpen className="status-icon processing" />;
      case 'shipped':
        return <FaShippingFast className="status-icon shipped" />;
      case 'delivered':
        return <FaCheckCircle className="status-icon delivered" />;
      case 'cancelled':
        return <FaTimes className="status-icon cancelled" />;
      default:
        return null;
    }
  };

  // Handle delete item click
  const handleDeleteItemClick = (itemId) => {
    setDeleteItemId(itemId);
    setShowDeleteConfirm(true);
    setDeleteError(null);
  };

  // Handle delete item confirm
  const handleDeleteItemConfirm = async () => {
    if (!deleteItemId) return;

    try {
      setDeleteLoading(true);
      setDeleteError(null);

      // Call API to delete item
      const response = await ordersAPI.deleteOrderItem(orderId, deleteItemId);

      // Update order with new data
      setOrder(response.data);

      // Close modal
      setShowDeleteConfirm(false);
      setDeleteItemId(null);

    } catch (err) {
      console.error('Error deleting order item:', err);
      setDeleteError('Failed to delete item. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  // Handle delete item cancel
  const handleDeleteItemCancel = () => {
    setShowDeleteConfirm(false);
    setDeleteItemId(null);
    setDeleteError(null);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading order details...</p>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{error || 'Order not found'}</p>
        <button onClick={() => navigate('/admin/orders')} className="btn btn-primary">
          Back to Orders
        </button>
      </div>
    );
  }

  return (
    <div className="admin-order-detail-page">
      <div className="admin-header">
        <button
          className="btn btn-icon"
          onClick={() => navigate('/admin/orders')}
        >
          <FaArrowLeft /> Back to Orders
        </button>
        <h1>Order #{order.id}</h1>

        <div className="admin-actions">
          <button
            className="btn btn-secondary"
            onClick={handlePrint}
            title="Print order"
          >
            <FaPrint /> Print
          </button>
          <button
            className="btn btn-secondary"
            onClick={handleDownloadPDF}
            title="Download as PDF"
          >
            <FaDownload /> Download PDF
          </button>
        </div>
      </div>

      <div className="admin-order-status-card">
        <div className="status-header">
          <h2>Order Status</h2>
          {statusUpdateSuccess && (
            <div className="status-success-message">
              <FaCheck /> Status updated successfully
            </div>
          )}
        </div>

        <div className="status-content">
          <div className="status-info">
            {getStatusIcon(order.status)}
            <div className="status-details">
              <div className="status-label">Current Status:</div>
              <div className={`status-value status-${order.status}`}>
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </div>
              <div className="status-date">
                Last updated: {formatDate(order.updated_at || order.created_at)}
              </div>
            </div>
          </div>

          <div className="status-update">
            <label htmlFor="status-select">Update Status:</label>
            <div className="status-select-wrapper">
              <select
                id="status-select"
                className={`status-select status-${order.status}`}
                value={order.status}
                onChange={(e) => handleStatusUpdate(e.target.value)}
                disabled={statusUpdateLoading}
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {statusUpdateLoading && <div className="status-loading-spinner"></div>}
            </div>
          </div>
        </div>
      </div>

      <div className="admin-order-sections" ref={printRef}>
        <div className="print-header" style={{ display: 'none' }}>
          <h1>Order #{order.id}</h1>
          <p>Date: {formatDate(order.created_at)}</p>
          <p>Status: {order.status.charAt(0).toUpperCase() + order.status.slice(1)}</p>
        </div>

        <div className="admin-order-section">
          <h2>Customer Information</h2>
          <div className="admin-order-info-grid">
            <div className="info-item">
              <strong>Name:</strong> {order.full_name}
            </div>
            <div className="info-item">
              <strong>Email:</strong> {order.email}
            </div>
            <div className="info-item">
              <strong>Phone:</strong> {order.phone}
            </div>
            <div className="info-item">
              <strong>Address:</strong> {order.address}
            </div>
            <div className="info-item">
              <strong>City:</strong> {order.city}
            </div>
            <div className="info-item">
              <strong>State:</strong> {order.state}
            </div>
          </div>
        </div>

        <div className="admin-order-section">
          <div className="section-header">
            <h2>Order Items</h2>
            <div className="order-date">
              <strong>Order Date:</strong> {formatDate(order.created_at)}
            </div>
          </div>

          {order.items.length === 0 ? (
            <div className="empty-state">
              <p>No items in this order.</p>
            </div>
          ) : (
            <div className="admin-order-items">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Quantity</th>
                    <th>Total</th>
                    {/* Only show Actions column when not printing */}
                    <th className="no-print">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {order.items.map(item => (
                    <tr key={item.id}>
                      <td>
                        <div className="product-info">
                          <div className="product-name">{item.product.name}</div>
                          <div className="product-category">{item.product.category_name}</div>
                        </div>
                      </td>
                      <td>{item.price} DT</td>
                      <td>{item.quantity}</td>
                      <td>{(item.price * item.quantity).toFixed(2)} DT</td>
                      {/* Only show Actions column when not printing */}
                      <td className="no-print">
                        <button
                          className="btn-icon btn-danger"
                          onClick={() => handleDeleteItemClick(item.id)}
                          aria-label="Delete item"
                        >
                          <FaTrash />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3" className="text-right"><strong>Total:</strong></td>
                    <td colSpan="2"><strong>{order.total_price} DT</strong></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h3>Confirm Deletion</h3>
            </div>
            <div className="modal-body">
              <p>Are you sure you want to delete this item from the order?</p>
              <p>This action cannot be undone.</p>

              {deleteError && (
                <div className="error-message">
                  {deleteError}
                </div>
              )}
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-outline"
                onClick={handleDeleteItemCancel}
                disabled={deleteLoading}
              >
                <FaTimes /> Cancel
              </button>
              <button
                className="btn btn-danger"
                onClick={handleDeleteItemConfirm}
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <span>Deleting...</span>
                ) : (
                  <><FaCheck /> Confirm</>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminOrderDetail;
