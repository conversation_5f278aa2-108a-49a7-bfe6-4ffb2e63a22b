/**
 * Profile utilities for managing user profile data and auto-fill functionality
 */

/**
 * Extract profile data from user object for checkout auto-fill
 * @param {Object} user - Current user object from AuthContext
 * @returns {Object} - Formatted profile data for checkout form
 */
export const extractProfileDataForCheckout = (user) => {
  if (!user) {
    return {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
    };
  }

  return {
    fullName: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
    email: user.email || '',
    phone: user.profile?.phone_number || '',
    address: user.profile?.address || '',
    city: user.profile?.city || '',
    state: user.profile?.state || '',
  };
};

/**
 * Extract profile data from user object for profile form
 * @param {Object} user - Current user object from AuthContext
 * @returns {Object} - Formatted profile data for profile form
 */
export const extractProfileDataForForm = (user) => {
  if (!user) {
    return {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
    };
  }

  return {
    firstName: user.first_name || '',
    lastName: user.last_name || '',
    email: user.email || '',
    phoneNumber: user.profile?.phone_number || '',
    address: user.profile?.address || '',
    city: user.profile?.city || '',
    state: user.profile?.state || '',
    zipCode: user.profile?.zip_code || '',
  };
};

/**
 * Save profile data to localStorage for guest users
 * @param {Object} profileData - Profile data to save
 */
export const saveGuestProfileData = (profileData) => {
  try {
    const guestProfile = {
      fullName: profileData.fullName || '',
      email: profileData.email || '',
      phone: profileData.phone || '',
      address: profileData.address || '',
      city: profileData.city || '',
      state: profileData.state || '',
      savedAt: new Date().toISOString(),
    };
    
    localStorage.setItem('guestProfile', JSON.stringify(guestProfile));
    console.log('✅ Guest profile data saved to localStorage');
  } catch (error) {
    console.error('❌ Error saving guest profile data:', error);
  }
};

/**
 * Load profile data from localStorage for guest users
 * @returns {Object} - Saved guest profile data or empty object
 */
export const loadGuestProfileData = () => {
  try {
    const savedProfile = localStorage.getItem('guestProfile');
    if (savedProfile) {
      const profileData = JSON.parse(savedProfile);
      console.log('✅ Guest profile data loaded from localStorage');
      return profileData;
    }
  } catch (error) {
    console.error('❌ Error loading guest profile data:', error);
  }
  
  return {
    fullName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
  };
};

/**
 * Clear guest profile data from localStorage
 */
export const clearGuestProfileData = () => {
  try {
    localStorage.removeItem('guestProfile');
    console.log('✅ Guest profile data cleared from localStorage');
  } catch (error) {
    console.error('❌ Error clearing guest profile data:', error);
  }
};

/**
 * Check if profile data is complete for checkout
 * @param {Object} profileData - Profile data to validate
 * @returns {Object} - Validation result with isComplete boolean and missing fields array
 */
export const validateProfileCompleteness = (profileData) => {
  const requiredFields = ['fullName', 'email', 'phone', 'address', 'city'];
  const missingFields = [];

  requiredFields.forEach(field => {
    if (!profileData[field] || profileData[field].trim() === '') {
      missingFields.push(field);
    }
  });

  return {
    isComplete: missingFields.length === 0,
    missingFields,
    completionPercentage: Math.round(((requiredFields.length - missingFields.length) / requiredFields.length) * 100)
  };
};

/**
 * Format profile data for display
 * @param {Object} profileData - Profile data to format
 * @returns {Object} - Formatted profile data with display labels
 */
export const formatProfileForDisplay = (profileData) => {
  return {
    'Full Name': profileData.fullName || 'Not provided',
    'Email': profileData.email || 'Not provided',
    'Phone': profileData.phone || 'Not provided',
    'Address': profileData.address || 'Not provided',
    'City': profileData.city || 'Not provided',
    'State': profileData.state || 'Not provided',
  };
};
