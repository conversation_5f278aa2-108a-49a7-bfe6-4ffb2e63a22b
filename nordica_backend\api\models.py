from django.db import models
from django.contrib.auth.models import User


class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.CharField(max_length=50, blank=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='categories/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.name


class Product(models.Model):
    name = models.CharField(max_length=200)
    slug = models.CharField(max_length=50, blank=True)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    stock = models.PositiveIntegerField(default=0)
    is_available = models.<PERSON>oleanField(default=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ProductImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/')
    is_primary = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.name} - Image"


class Pack(models.Model):
    name = models.CharField(max_length=200)
    slug = models.CharField(max_length=50, blank=True)
    description = models.TextField()
    products = models.ManyToManyField(Product, through='PackProduct', related_name='packs')
    original_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Sum of individual product prices")
    pack_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Discounted pack price")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    image = models.ImageField(upload_to='packs/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Calculate discount percentage
        if self.original_price and self.pack_price:
            self.discount_percentage = ((self.original_price - self.pack_price) / self.original_price) * 100
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    @property
    def savings(self):
        return self.original_price - self.pack_price if self.original_price and self.pack_price else 0


class PackProduct(models.Model):
    pack = models.ForeignKey(Pack, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('pack', 'product')

    def __str__(self):
        return f"{self.pack.name} - {self.product.name} (x{self.quantity})"


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    zip_code = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} Profile"


class Cart(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Cart"


class CartItem(models.Model):
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, null=True, blank=True)
    pack = models.ForeignKey(Pack, on_delete=models.CASCADE, null=True, blank=True)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(product__isnull=False) | models.Q(pack__isnull=False),
                name='cart_item_has_product_or_pack'
            ),
            models.CheckConstraint(
                check=~(models.Q(product__isnull=False) & models.Q(pack__isnull=False)),
                name='cart_item_not_both_product_and_pack'
            ),
        ]

    def __str__(self):
        if self.product:
            return f"{self.product.name} x {self.quantity}"
        elif self.pack:
            return f"{self.pack.name} (Pack) x {self.quantity}"
        return f"Cart Item x {self.quantity}"

    @property
    def item_name(self):
        return self.product.name if self.product else self.pack.name

    @property
    def item_price(self):
        return self.product.price if self.product else self.pack.pack_price

    @property
    def total_price(self):
        return self.item_price * self.quantity


class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    shipping_address = models.TextField()
    phone_number = models.CharField(max_length=20)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.user:
            return f"Order #{self.id} - {self.user.username}"
        else:
            return f"Order #{self.id} - Guest ({self.email})"


class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"





class SiteSettings(models.Model):
    # Basic Settings
    site_name = models.CharField(max_length=100, default="Nordica Nutrition", help_text="Website name")
    site_description = models.TextField(blank=True, help_text="Website description")
    maintenance_mode = models.BooleanField(default=False, help_text="Enable maintenance mode")

    # Contact Information
    phone_number = models.CharField(max_length=20, blank=True, help_text="Contact phone number")
    email = models.EmailField(blank=True, help_text="Contact email address")
    address = models.TextField(blank=True, help_text="Physical address")

    # Social Media
    facebook_url = models.URLField(blank=True, help_text="Facebook page URL")
    instagram_url = models.URLField(blank=True, help_text="Instagram page URL")
    tiktok_url = models.URLField(blank=True, help_text="TikTok page URL")

    # Pricing & Delivery
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Tax percentage (e.g., 19.00 for 19%)")
    delivery_fee = models.DecimalField(max_digits=10, decimal_places=2, default=7.00, help_text="Standard delivery fee in TND")
    free_delivery_threshold = models.DecimalField(max_digits=10, decimal_places=2, default=100.00, help_text="Minimum order amount for free delivery in TND")

    # Business Hours
    business_hours = models.TextField(blank=True, default="Mon-Fri: 9:00 AM - 6:00 PM\nSat: 9:00 AM - 2:00 PM\nSun: Closed", help_text="Business hours")

    # Additional Info
    about_us = models.TextField(blank=True, help_text="About us content")
    terms_conditions = models.TextField(blank=True, help_text="Terms and conditions")
    privacy_policy = models.TextField(blank=True, help_text="Privacy policy")

    class Meta:
        verbose_name = "Site Settings"
        verbose_name_plural = "Site Settings"

    def __str__(self):
        return f"Site Settings - {self.site_name}"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and SiteSettings.objects.exists():
            raise ValueError("Only one SiteSettings instance is allowed")
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """Get or create site settings"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
