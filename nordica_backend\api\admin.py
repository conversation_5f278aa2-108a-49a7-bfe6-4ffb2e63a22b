from django.contrib import admin
from django.utils.html import format_html
from .models import *

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'image_preview', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />', obj.image.url)
        return "No Image"
    image_preview.short_description = "Image"

class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1
    fields = ['image', 'is_primary', 'image_preview']
    readonly_fields = ['image_preview']

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="100" height="100" style="object-fit: cover; border-radius: 4px;" />', obj.image.url)
        return "No Image"
    image_preview.short_description = "Preview"

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'image_preview', 'category', 'price', 'stock', 'is_available', 'is_featured']
    list_filter = ['category', 'is_available', 'is_featured', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ProductImageInline]

    def image_preview(self, obj):
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image and primary_image.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />', primary_image.image.url)

        first_image = obj.images.first()
        if first_image and first_image.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />', first_image.image.url)

        return "No Image"
    image_preview.short_description = "Image"

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'status', 'total_amount', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__username', 'phone_number']

@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ['product', 'image_preview', 'is_primary', 'created_at']
    list_filter = ['is_primary', 'created_at']
    search_fields = ['product__name']

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="100" height="100" style="object-fit: cover; border-radius: 4px;" />', obj.image.url)
        return "No Image"
    image_preview.short_description = "Preview"

class PackProductInline(admin.TabularInline):
    model = PackProduct
    extra = 1
    fields = ['product', 'quantity']

@admin.register(Pack)
class PackAdmin(admin.ModelAdmin):
    list_display = ['name', 'pack_price', 'original_price', 'discount_percentage', 'is_active', 'is_featured', 'image_preview', 'created_at']
    list_filter = ['is_active', 'is_featured', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [PackProductInline]
    readonly_fields = ['discount_percentage', 'savings', 'image_preview']
    fields = ['name', 'slug', 'description', 'image', 'image_preview', 'original_price', 'pack_price', 'discount_percentage', 'savings', 'is_active', 'is_featured']

    def savings(self, obj):
        return f"{obj.savings:.2f} TND" if obj.savings else "0.00 TND"
    savings.short_description = "Savings"

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />', obj.image.url)
        return "No Image"
    image_preview.short_description = "Image Preview"
    image_preview.allow_tags = True

@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    list_display = ['site_name', 'phone_number', 'email', 'delivery_fee', 'free_delivery_threshold', 'maintenance_mode']

    fieldsets = (
        ('Basic Settings', {
            'fields': ('site_name', 'site_description', 'maintenance_mode'),
            'description': 'Basic website settings'
        }),
        ('Contact Information', {
            'fields': ('phone_number', 'email', 'address', 'business_hours'),
            'description': 'Contact details displayed on the website'
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'instagram_url', 'tiktok_url'),
            'description': 'Social media links'
        }),
        ('Pricing & Delivery', {
            'fields': ('tax_percentage', 'delivery_fee', 'free_delivery_threshold'),
            'description': 'Pricing and delivery settings that affect checkout'
        }),
        ('Content Pages', {
            'fields': ('about_us', 'terms_conditions', 'privacy_policy'),
            'description': 'Content for static pages',
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion
        return False

admin.site.register(UserProfile)
admin.site.register(Cart)
admin.site.register(CartItem)
admin.site.register(OrderItem)
