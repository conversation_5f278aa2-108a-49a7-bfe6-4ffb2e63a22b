import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { productsAPI, categoriesAPI } from '../../services/api';

const AdminProducts = () => {
  const navigate = useNavigate();
  
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Filtering and pagination state
  const [filters, setFilters] = useState({
    category: '',
    search: '',
    availability: '',
  });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await categoriesAPI.getAll();
        setCategories(response.data.results || []);
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };
    
    fetchCategories();
  }, []);
  
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Prepare query parameters
        const params = {
          page,
          search: filters.search,
        };
        
        // Add category filter if specified
        if (filters.category) {
          params.category = filters.category;
        }
        
        // Add availability filter if specified
        if (filters.availability === 'available') {
          params.is_available = true;
        } else if (filters.availability === 'unavailable') {
          params.is_available = false;
        }
        
        // Fetch products with filters
        const response = await productsAPI.getAll(params);
        setProducts(response.data.results || []);
        
        // Calculate total pages
        const count = response.data.count || 0;
        const pageSize = response.data.page_size || 10;
        setTotalPages(Math.ceil(count / pageSize));
        
      } catch (err) {
        setError('Failed to load products');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, [page, filters]);
  
  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change
  };
  
  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    // The search is already handled by the useEffect when filters.search changes
  };
  
  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    window.scrollTo(0, 0);
  };
  
  return (
    <div className="admin-products-page">
      <div className="admin-header">
        <h1>Products</h1>
        <button 
          className="btn btn-primary"
          onClick={() => navigate('/admin/products/new')}
        >
          <i className="fas fa-plus"></i> Add New Product
        </button>
      </div>
      
      <div className="admin-filters">
        <form className="search-form" onSubmit={handleSearch}>
          <input
            type="text"
            name="search"
            placeholder="Search products..."
            value={filters.search}
            onChange={handleFilterChange}
          />
          <button type="submit" className="btn btn-primary">
            <i className="fas fa-search"></i>
          </button>
        </form>
        
        <div className="filter-controls">
          <div className="filter-group">
            <label htmlFor="category">Category:</label>
            <select
              id="category"
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="availability">Availability:</label>
            <select
              id="availability"
              name="availability"
              value={filters.availability}
              onChange={handleFilterChange}
            >
              <option value="">All</option>
              <option value="available">In Stock</option>
              <option value="unavailable">Out of Stock</option>
            </select>
          </div>
        </div>
      </div>
      
      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading products...</p>
        </div>
      ) : error ? (
        <div className="error-container">
          <p>{error}</p>
          <button onClick={() => window.location.reload()} className="btn btn-primary">
            Try Again
          </button>
        </div>
      ) : products.length === 0 ? (
        <div className="empty-state">
          <p>No products found matching your criteria.</p>
          <button 
            onClick={() => {
              setFilters({ category: '', search: '', availability: '' });
              setPage(1);
            }} 
            className="btn btn-outline"
          >
            Clear Filters
          </button>
        </div>
      ) : (
        <>
          <div className="table-responsive">
            <table className="admin-table">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {products.map(product => (
                  <tr key={product.id}>
                    <td className="product-image-cell">
                      {product.images && product.images.length > 0 ? (
                        <img 
                          src={product.images[0].image} 
                          alt={product.name} 
                          className="product-thumbnail"
                        />
                      ) : (
                        <div className="product-thumbnail-placeholder">
                          No Image
                        </div>
                      )}
                    </td>
                    <td>{product.name}</td>
                    <td>{product.category_name}</td>
                    <td>{product.price} DT</td>
                    <td>{product.stock}</td>
                    <td>
                      <span className={`status-badge ${product.is_available ? 'status-active' : 'status-inactive'}`}>
                        {product.is_available ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="actions-cell">
                      <button 
                        className="btn-icon"
                        onClick={() => navigate(`/admin/products/edit/${product.id}`)}
                        aria-label="Edit product"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button 
                        className="btn-icon"
                        onClick={() => navigate(`/products/${product.id}`)}
                        aria-label="View product"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination">
              <button 
                onClick={() => handlePageChange(page - 1)}
                disabled={page === 1}
                className="pagination-button"
              >
                Previous
              </button>
              
              <div className="pagination-pages">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(p => p === 1 || p === totalPages || (p >= page - 1 && p <= page + 1))
                  .map(p => (
                    <button
                      key={p}
                      onClick={() => handlePageChange(p)}
                      className={`pagination-page ${p === page ? 'active' : ''}`}
                    >
                      {p}
                    </button>
                  ))}
              </div>
              
              <button 
                onClick={() => handlePageChange(page + 1)}
                disabled={page === totalPages}
                className="pagination-button"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AdminProducts;
