# Generated by Django 5.2.4 on 2025-07-27 14:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0003_pack_packproduct_pack_products'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='about_us',
            field=models.TextField(blank=True, help_text='About us content'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='business_hours',
            field=models.TextField(blank=True, default='Mon-Fri: 9:00 AM - 6:00 PM\nSat: 9:00 AM - 2:00 PM\nSun: Closed', help_text='Business hours'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='privacy_policy',
            field=models.TextField(blank=True, help_text='Privacy policy'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='site_description',
            field=models.TextField(blank=True, help_text='Website description'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='site_name',
            field=models.CharField(default='Nordica Nutrition', help_text='Website name', max_length=100),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='terms_conditions',
            field=models.TextField(blank=True, help_text='Terms and conditions'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='address',
            field=models.TextField(blank=True, help_text='Physical address'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='delivery_fee',
            field=models.DecimalField(decimal_places=2, default=7.0, help_text='Standard delivery fee in TND', max_digits=10),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='email',
            field=models.EmailField(blank=True, help_text='Contact email address', max_length=254),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='facebook_url',
            field=models.URLField(blank=True, help_text='Facebook page URL'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='free_delivery_threshold',
            field=models.DecimalField(decimal_places=2, default=100.0, help_text='Minimum order amount for free delivery in TND', max_digits=10),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='instagram_url',
            field=models.URLField(blank=True, help_text='Instagram page URL'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='maintenance_mode',
            field=models.BooleanField(default=False, help_text='Enable maintenance mode'),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='phone_number',
            field=models.CharField(blank=True, help_text='Contact phone number', max_length=20),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='tax_percentage',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Tax percentage (e.g., 19.00 for 19%)', max_digits=5),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='tiktok_url',
            field=models.URLField(blank=True, help_text='TikTok page URL'),
        ),
    ]
