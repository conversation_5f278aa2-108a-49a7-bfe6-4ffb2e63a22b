import { createContext, useState, useContext, useEffect } from 'react';
import { settingsAPI } from '../services/api';

// Create the context
const SiteSettingsContext = createContext();

// Custom hook to use the site settings context
export const useSiteSettings = () => {
  return useContext(SiteSettingsContext);
};

// Provider component
export const SiteSettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState({
    site_name: 'Nordica Nutrition',
    site_description: '',
    phone_number: '',
    email: '',
    address: '',
    business_hours: '',
    facebook_url: '',
    instagram_url: '',
    tiktok_url: '',
    tax_percentage: 0.00,
    delivery_fee: 7.00,
    free_delivery_threshold: 100.00,
    about_us: '',
    terms_conditions: '',
    privacy_policy: '',
    maintenance_mode: false
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch site settings
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await settingsAPI.getSettings();
      console.log('✅ Site settings fetched:', response.data);
      setSettings(response.data);
    } catch (err) {
      console.error('❌ Error fetching site settings:', err);
      setError('Failed to load site settings');
      // Keep default settings on error
    } finally {
      setLoading(false);
    }
  };

  // Fetch settings on mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Helper functions for common settings
  const getDeliveryFee = (subtotal = 0) => {
    const threshold = parseFloat(settings.free_delivery_threshold) || 0;
    const fee = parseFloat(settings.delivery_fee) || 0;
    
    if (threshold > 0 && subtotal >= threshold) {
      return 0; // Free delivery
    }
    return fee;
  };

  const getTaxAmount = (subtotal = 0) => {
    const taxPercentage = parseFloat(settings.tax_percentage) || 0;
    return (subtotal * taxPercentage) / 100;
  };

  const isMaintenanceMode = () => {
    return settings.maintenance_mode === true;
  };

  const getContactInfo = () => {
    return {
      phone: settings.phone_number,
      email: settings.email,
      address: settings.address,
      businessHours: settings.business_hours
    };
  };

  const getSocialLinks = () => {
    return {
      facebook: settings.facebook_url,
      instagram: settings.instagram_url,
      tiktok: settings.tiktok_url
    };
  };

  const value = {
    settings,
    loading,
    error,
    fetchSettings,
    
    // Helper functions
    getDeliveryFee,
    getTaxAmount,
    isMaintenanceMode,
    getContactInfo,
    getSocialLinks,
  };

  return (
    <SiteSettingsContext.Provider value={value}>
      {children}
    </SiteSettingsContext.Provider>
  );
};
