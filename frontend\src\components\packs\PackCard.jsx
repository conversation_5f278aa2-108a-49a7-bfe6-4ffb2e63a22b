import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import { FaShopping<PERSON>art, FaSpinner, FaTag, FaGift } from 'react-icons/fa';

const PackCard = ({ pack }) => {
  const { addPackToCart } = useCart();
  const [addingToCart, setAddingToCart] = useState(false);

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    setAddingToCart(true);
    try {
      // Add pack to cart
      await addPackToCart(pack, 1);
    } catch (error) {
      console.error('Error adding pack to cart:', error);
    } finally {
      setAddingToCart(false);
    }
  };

  return (
    <div className="pack-card">
      <Link to={`/packs/${pack.id}`} className="pack-card-link">
        {/* Pack Image */}
        <div className="pack-image-container">
          {pack.image ? (
            <img 
              src={pack.image} 
              alt={pack.name}
              className="pack-image"
            />
          ) : (
            <div className="pack-image-placeholder">
              <FaGift size={40} />
            </div>
          )}
          
          {/* Discount Badge */}
          {pack.discount_percentage > 0 && (
            <div className="pack-discount-badge">
              <FaTag />
              {Math.round(pack.discount_percentage)}% OFF
            </div>
          )}
        </div>

        {/* Pack Info */}
        <div className="pack-info">
          <h3 className="pack-name">{pack.name}</h3>
          <p className="pack-description">{pack.description}</p>
          
          {/* Products in Pack */}
          <div className="pack-products">
            <span className="pack-products-label">Includes:</span>
            <ul className="pack-products-list">
              {pack.pack_products?.slice(0, 3).map((packProduct, index) => (
                <li key={index}>
                  {packProduct.product.name} 
                  {packProduct.quantity > 1 && ` (x${packProduct.quantity})`}
                </li>
              ))}
              {pack.pack_products?.length > 3 && (
                <li>+{pack.pack_products.length - 3} more items</li>
              )}
            </ul>
          </div>

          {/* Pricing */}
          <div className="pack-pricing">
            <div className="pack-prices">
              <span className="pack-original-price">{pack.original_price} TND</span>
              <span className="pack-current-price">{pack.pack_price} TND</span>
            </div>
            <div className="pack-savings">
              You save: {pack.savings} TND
            </div>
          </div>
        </div>
      </Link>

      {/* Add to Cart Button */}
      <button
        onClick={handleAddToCart}
        disabled={addingToCart}
        className="pack-add-to-cart-btn"
      >
        {addingToCart ? (
          <>
            <FaSpinner className="spinning" />
            Adding...
          </>
        ) : (
          <>
            <FaShoppingCart />
            Add Pack to Cart
          </>
        )}
      </button>
    </div>
  );
};

export default PackCard;
