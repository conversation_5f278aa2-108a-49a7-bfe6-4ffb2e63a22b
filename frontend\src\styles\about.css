/* ABOUT US PAGE - CLEAN & SIMPLE */

/* Page Container */
.about-us-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
  background: var(--gray-light);
  min-height: 100vh;
}

/* Page Header */
.about-page-header {
  margin-bottom: var(--space-8);
  text-align: center;
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.about-page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--space-3);
  position: relative;
  display: inline-block;
  padding-bottom: var(--space-3);
}

.about-page-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.about-page-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.about-page-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.about-page-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
}

/* About Sections */
.about-section {
  margin-bottom: var(--space-12);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  align-items: center;
  margin-bottom: var(--space-8);
}

.about-content.reverse {
  direction: rtl;
}

.about-content.reverse > * {
  direction: ltr;
}

.about-text h2 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  position: relative;
  padding-bottom: var(--space-2);
}

.about-text h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--red);
  border-radius: 2px;
}

.about-text p {
  color: var(--gray);
  line-height: 1.7;
  margin-bottom: var(--space-4);
  font-size: 1.1rem;
}

.about-image {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.about-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s;
}

.about-image:hover img {
  transform: scale(1.05);
}

/* Values Grid */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.value-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.2s;
}

.value-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.value-icon {
  width: 60px;
  height: 60px;
  background: var(--red);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
}

.value-icon i {
  font-size: 1.5rem;
  color: var(--white);
}

.value-card h3 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-3);
}

.value-card p {
  color: var(--gray);
  line-height: 1.6;
}

/* Team Grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.team-member {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.2s;
}

.team-member:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.team-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto var(--space-4);
  border: 4px solid var(--red);
}

.team-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-member h3 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-2);
}

.team-role {
  color: var(--red);
  font-weight: 600;
  margin-bottom: var(--space-3);
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.team-member p:not(.team-role) {
  color: var(--gray);
  line-height: 1.6;
}

/* About List */
.about-list {
  list-style: none;
  padding: 0;
  margin: var(--space-4) 0;
}

.about-list li {
  position: relative;
  padding-left: var(--space-6);
  margin-bottom: var(--space-2);
  color: var(--gray);
  line-height: 1.6;
}

.about-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--red);
  font-weight: 700;
  font-size: 1.1rem;
}

/* CTA Section */
.about-cta {
  background: var(--gray-light);
  border-radius: 8px;
  padding: var(--space-8);
  text-align: center;
  margin-top: var(--space-12);
}

.about-cta h2 {
  font-size: 2.5rem;
  color: var(--black);
  margin-bottom: var(--space-4);
}

.about-cta p {
  font-size: 1.2rem;
  color: var(--gray);
  margin-bottom: var(--space-6);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

/* Section Titles */
.about-section h2.text-center {
  text-align: center;
  font-size: 2.5rem;
  color: var(--black);
  margin-bottom: var(--space-8);
  position: relative;
  padding-bottom: var(--space-4);
}

.about-section h2.text-center::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--red);
  border-radius: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-us-page {
    padding: var(--space-3);
  }

  .about-page-header {
    padding: var(--space-6) var(--space-4);
    border-radius: 8px;
  }

  .about-page-header h1 {
    font-size: 2rem;
  }

  .about-page-header p {
    font-size: 1rem;
  }

  .about-section {
    padding: var(--space-8) var(--space-4);
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
    text-align: center;
  }

  .about-content.reverse {
    direction: ltr;
  }

  .about-text h2 {
    font-size: 1.8rem;
    margin-bottom: var(--space-4);
  }

  .about-text p {
    font-size: 1rem;
    line-height: 1.6;
  }

  .values-grid,
  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .value-card,
  .team-card {
    padding: var(--space-4);
  }

  .value-card h3,
  .team-card h3 {
    font-size: 1.3rem;
  }

  .about-cta {
    padding: var(--space-8) var(--space-4);
  }

  .about-cta h2 {
    font-size: 2rem;
    margin-bottom: var(--space-4);
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
  }

  .btn {
    width: 100%;
    max-width: 300px;
    padding: var(--space-3) var(--space-4);
  }

  .about-section h2.text-center {
    font-size: 2rem;
    margin-bottom: var(--space-6);
  }
}

@media (max-width: 480px) {
  .about-us-page {
    padding: var(--space-2);
  }

  .about-page-header {
    padding: var(--space-4) var(--space-3);
    border-radius: 6px;
  }

  .about-page-header h1 {
    font-size: 1.8rem;
  }

  .about-page-header h1::after {
    width: 60px;
    height: 2px;
  }

  .about-page-header p {
    font-size: 0.9rem;
  }

  .about-section {
    padding: var(--space-6) var(--space-3);
  }

  .about-text h2 {
    font-size: 1.5rem;
  }

  .about-text p {
    font-size: 0.9rem;
  }

  .value-card,
  .team-card {
    padding: var(--space-3);
  }

  .value-card h3,
  .team-card h3 {
    font-size: 1.2rem;
  }

  .about-cta {
    padding: var(--space-6) var(--space-3);
  }

  .about-cta h2 {
    font-size: 1.8rem;
  }

  .about-section h2.text-center {
    font-size: 1.8rem;
  }
}
