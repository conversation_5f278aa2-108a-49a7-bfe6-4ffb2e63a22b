/* PRODUCT DETAIL PAGE - ENHANCED & CLEAN */

/* Page Container */
.product-detail-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-4);
  min-height: 100vh;
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-8);
  font-size: 0.9rem;
  color: var(--gray);
  background: var(--gray-light);
  padding: var(--space-3) var(--space-4);
  border-radius: 8px;
  border: 1px solid var(--border);
}

.breadcrumb a {
  color: var(--gray);
  text-decoration: none;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: 500;
}

.breadcrumb a:hover {
  color: var(--red);
}

.breadcrumb span {
  color: var(--black);
  font-weight: 600;
}

.breadcrumb svg {
  color: var(--border);
}

/* Product Detail Container */
.product-detail-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
  background: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: var(--space-8);
  border: 1px solid var(--border);
}

/* Product Images Section */
.product-detail-left {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.main-image-container {
  position: relative;
  width: 100%;
  height: 450px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  background: var(--white);
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.05);
}

.main-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-light);
  color: var(--gray);
  font-size: 1.1rem;
  font-weight: 500;
}

.thumbnail-images {
  display: flex;
  gap: var(--space-3);
  overflow-x: auto;
  padding: var(--space-2) 0;
}

.thumbnail {
  width: 90px;
  height: 90px;
  border-radius: 8px;
  overflow: hidden;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  box-shadow: var(--shadow);
}

.thumbnail:hover {
  border-color: var(--red);
  transform: translateY(-2px);
}

.thumbnail.active {
  border-color: var(--red);
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Product Info Section */
.product-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.product-header {
  border-bottom: 1px solid var(--border);
  padding-bottom: var(--space-4);
}

.product-name {
  font-size: 2.5rem;
  color: var(--black);
  margin: 0 0 var(--space-3) 0;
  font-weight: 700;
  line-height: 1.2;
}

.product-meta {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  flex-wrap: wrap;
}

.product-category {
  color: var(--red);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: var(--space-1) var(--space-3);
  background: rgba(220, 38, 38, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(220, 38, 38, 0.2);
  transition: all 0.2s;
}

.product-category:hover {
  background: var(--red);
  color: var(--white);
}

.product-availability {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.in-stock {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: #166534;
  font-weight: 600;
  font-size: 0.9rem;
}

.out-of-stock {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: #dc2626;
  font-weight: 600;
  font-size: 0.9rem;
}

.product-price-large {
  font-size: 2.8rem;
  color: var(--red);
  font-weight: 800;
  margin: 0;
  position: relative;
}

.product-price-large::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, var(--red), transparent);
  border-radius: 2px;
}

/* Product Actions Section */
.product-actions-cool {
  background: var(--gray-light);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-6);
  margin: var(--space-6) 0;
}

.product-actions-row {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.quantity-control-cool {
  display: flex;
  align-items: center;
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.quantity-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: var(--white);
  color: var(--gray);
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
  background: var(--red);
  color: var(--white);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-control-cool input {
  width: 80px;
  height: 44px;
  border: none;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  background: var(--white);
  color: var(--black);
}

.quantity-control-cool input:focus {
  outline: none;
  background: var(--gray-light);
}

.cart-btn {
  flex: 1;
  padding: var(--space-3) var(--space-6);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  min-height: 44px;
  box-shadow: var(--shadow);
}

.cart-btn:hover:not(:disabled) {
  background: var(--red-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.cart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.stock-info-cool {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.stock-info-cool svg {
  color: var(--red);
}

/* Out of Stock Message */
.out-of-stock-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: var(--space-6);
  text-align: center;
  margin: var(--space-6) 0;
}

.out-of-stock-icon {
  font-size: 2rem;
  color: #dc2626;
  margin-bottom: var(--space-3);
}

.out-of-stock-message h3 {
  color: #dc2626;
  margin-bottom: var(--space-2);
  font-size: 1.3rem;
}

.out-of-stock-message p {
  color: #7f1d1d;
  margin: 0;
}

/* Product Tabs Section */
.product-tabs {
  margin-top: var(--space-6);
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.product-description,
.product-details {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  box-shadow: var(--shadow);
}

.product-description h2,
.product-details h2 {
  font-size: 1.5rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  position: relative;
  padding-bottom: var(--space-2);
}

.product-description h2::after,
.product-details h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background: var(--red);
  border-radius: 1px;
}

.product-description p {
  color: var(--gray);
  line-height: 1.8;
  font-size: 1.1rem;
  margin: 0;
}

.product-details ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product-details li {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--border);
  color: var(--gray);
  font-size: 1rem;
}

.product-details li:last-child {
  border-bottom: none;
}

.product-details li::before {
  content: '✓';
  color: var(--red);
  font-weight: 700;
  font-size: 1.1rem;
}

.product-details strong {
  color: var(--black);
}

/* Related Products Section */
.related-products {
  margin-top: var(--space-12);
  padding-top: var(--space-8);
  border-top: 1px solid var(--border);
}

.related-products h2 {
  font-size: 2.5rem;
  color: var(--black);
  margin-bottom: var(--space-8);
  text-align: center;
  position: relative;
  padding-bottom: var(--space-4);
}

.related-products h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--red);
  border-radius: 2px;
}

.related-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--space-4);
  padding: var(--space-3) 0;
}



/* Loading & Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-light);
  border-top: 4px solid var(--red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: var(--gray);
  font-size: 1.1rem;
  margin-bottom: var(--space-4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .product-detail-container {
    gap: var(--space-6);
  }

  .product-name {
    font-size: 2rem;
  }

  .product-price-large {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  .product-detail-page {
    padding: var(--space-4) var(--space-2);
  }

  .breadcrumb {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }

  .product-detail-container {
    grid-template-columns: 1fr;
    gap: var(--space-6);
    padding: var(--space-4);
  }

  .product-name {
    font-size: 1.8rem;
  }

  .product-price-large {
    font-size: 2rem;
  }

  .main-image-container {
    height: 350px;
  }

  .thumbnail-images {
    justify-content: center;
  }

  .product-actions-row {
    flex-direction: column;
    gap: var(--space-3);
  }

  .quantity-control-cool {
    align-self: center;
  }

  .related-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-4);
  }
}

@media (max-width: 480px) {
  .related-products-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .related-products h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .product-detail-page {
    padding: var(--space-3) var(--space-2);
  }

  .product-detail-container {
    padding: var(--space-3);
  }

  .product-name {
    font-size: 1.5rem;
  }

  .product-price-large {
    font-size: 1.8rem;
  }

  .product-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .main-image-container {
    height: 280px;
  }

  .thumbnail {
    width: 70px;
    height: 70px;
  }

  .related-products-grid {
    grid-template-columns: 1fr;
  }

  .related-products h2 {
    font-size: 1.8rem;
  }
}
