import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import '../styles/auth.css';

const Register = () => {
  const { register, loading, error } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
  });
  const [formErrors, setFormErrors] = useState({});

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = 'Phone number is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        // Prepare user data for registration - simplified for direct API
        const userData = {
          username: formData.username,
          email: formData.email,
          password: formData.password,
          first_name: formData.firstName,
          last_name: formData.lastName,
          phone_number: formData.phoneNumber,
        };

        console.log('Submitting registration data:', userData);
        await register(userData);
        navigate('/');
      } catch (err) {
        console.error('Registration error:', err);
        // Handle specific error messages from the API
        if (err.response?.data) {
          const apiErrors = err.response.data;
          console.log('API errors:', apiErrors);
          const newErrors = {};

          // Check for detail field first (common for our custom errors)
          if (apiErrors.detail) {
            const errorDetail = apiErrors.detail.toLowerCase();

            if (errorDetail.includes('username already exists')) {
              newErrors.username = 'Username already exists';
            } else if (errorDetail.includes('email already exists')) {
              newErrors.email = 'Email already exists';
            } else if (errorDetail.includes('user profile already exists')) {
              newErrors.general = 'User profile already exists. Please try logging in.';
            } else {
              // Handle general error message
              newErrors.general = apiErrors.detail;
            }
          } else {
            // Process all error fields
            Object.keys(apiErrors).forEach(field => {
              const errorMessage = Array.isArray(apiErrors[field])
                ? apiErrors[field][0]
                : apiErrors[field];

              // Map backend field names to frontend field names
              switch(field) {
                case 'username':
                  newErrors.username = errorMessage;
                  break;
                case 'email':
                  newErrors.email = errorMessage;
                  break;
                case 'password':
                  newErrors.password = errorMessage;
                  break;
                case 'password2':
                  newErrors.confirmPassword = errorMessage;
                  break;
                case 'first_name':
                  newErrors.firstName = errorMessage;
                  break;
                case 'last_name':
                  newErrors.lastName = errorMessage;
                  break;
                case 'phone_number':
                  newErrors.phoneNumber = errorMessage;
                  break;
                case 'non_field_errors':
                  newErrors.general = errorMessage;
                  break;
                default:
                  // For any other fields, add to general errors
                  newErrors.general = newErrors.general
                    ? `${newErrors.general}; ${field}: ${errorMessage}`
                    : `${field}: ${errorMessage}`;
              }
            });
          }

          setFormErrors(newErrors);
        } else {
          setFormErrors({ general: 'Registration failed. Please try again.' });
        }
      }
    }
  };

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState('');

  // Check password strength
  const checkPasswordStrength = (password) => {
    if (!password) return '';

    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isLongEnough = password.length >= 8;

    const strength =
      (hasLowerCase ? 1 : 0) +
      (hasUpperCase ? 1 : 0) +
      (hasNumbers ? 1 : 0) +
      (hasSpecialChars ? 1 : 0) +
      (isLongEnough ? 1 : 0);

    if (strength === 0) return '';
    if (strength <= 2) return 'weak';
    if (strength <= 3) return 'medium';
    if (strength <= 4) return 'strong';
    return 'very-strong';
  };

  // Update password strength when password changes
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (name === 'password') {
      setPasswordStrength(checkPasswordStrength(value));
    }

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };



  return (
    <div className="auth-page">
      <div className="auth-container">
        <div className="auth-header">
          <div className="auth-logo">Nordica Nutrition</div>
          <h2 className="auth-title">Create Account</h2>
          <p className="auth-subtitle">Join us to shop premium supplements and track your orders</p>
        </div>

        {error && (
          <div className="error-container">
            <p>{error}</p>
          </div>
        )}

        {formErrors.general && (
          <div className="error-container">
            <p>{formErrors.general}</p>
          </div>
        )}

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="firstName" className="form-label">First Name</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                className={`form-control ${formErrors.firstName ? 'error' : ''}`}
                value={formData.firstName}
                onChange={handleChange}
                placeholder="Your first name"
              />
              {formErrors.firstName && <div className="form-error">{formErrors.firstName}</div>}
            </div>

            <div className="form-group">
              <label htmlFor="lastName" className="form-label">Last Name</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                className={`form-control ${formErrors.lastName ? 'error' : ''}`}
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Your last name"
              />
              {formErrors.lastName && <div className="form-error">{formErrors.lastName}</div>}
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="username" className="form-label">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              className={`form-control ${formErrors.username ? 'error' : ''}`}
              value={formData.username}
              onChange={handleChange}
              placeholder="Choose a username"
            />
            {formErrors.username && <div className="form-error">{formErrors.username}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="email" className="form-label">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              className={`form-control ${formErrors.email ? 'error' : ''}`}
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
            />
            {formErrors.email && <div className="form-error">{formErrors.email}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="phoneNumber" className="form-label">Phone Number</label>
            <input
              type="tel"
              id="phoneNumber"
              name="phoneNumber"
              className={`form-control ${formErrors.phoneNumber ? 'error' : ''}`}
              value={formData.phoneNumber}
              onChange={handleChange}
              placeholder="+216 XX XXX XXX"
            />
            {formErrors.phoneNumber && <div className="form-error">{formErrors.phoneNumber}</div>}
            <div className="form-help">We'll use this for order updates and delivery coordination</div>
          </div>

          <div className="form-group" style={{ position: 'relative' }}>
            <label htmlFor="password" className="form-label">Password</label>
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              className={`form-control ${formErrors.password ? 'error' : ''}`}
              value={formData.password}
              onChange={handlePasswordChange}
              placeholder="Create a strong password"
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
            </button>
            {formErrors.password && <div className="form-error">{formErrors.password}</div>}

            {formData.password && (
              <>
                <div className="password-strength">
                  <div className={`password-strength-bar ${passwordStrength}`}></div>
                </div>
                <div className="password-strength-text">
                  <span className={`weak ${passwordStrength === 'weak' ? 'active' : ''}`}>Weak</span>
                  <span className={`medium ${passwordStrength === 'medium' ? 'active' : ''}`}>Medium</span>
                  <span className={`strong ${passwordStrength === 'strong' ? 'active' : ''}`}>Strong</span>
                  <span className={`very-strong ${passwordStrength === 'very-strong' ? 'active' : ''}`}>Very Strong</span>
                </div>
              </>
            )}
          </div>

          <div className="form-group" style={{ position: 'relative' }}>
            <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirmPassword"
              name="confirmPassword"
              className={`form-control ${formErrors.confirmPassword ? 'error' : ''}`}
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="Confirm your password"
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <i className={`fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
            </button>
            {formErrors.confirmPassword && <div className="form-error">{formErrors.confirmPassword}</div>}
          </div>

          <div className="form-check">
            <input type="checkbox" id="terms" name="terms" required />
            <label htmlFor="terms">
              I agree to the <Link to="/terms">Terms of Service</Link> and <Link to="/privacy">Privacy Policy</Link>
            </label>
          </div>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Creating Account...' : 'Create Account'}
          </button>
        </form>



        <div className="auth-footer">
          <p>Already have an account? <Link to="/login">Sign In</Link></p>
        </div>
      </div>
    </div>
  );
};

export default Register;
