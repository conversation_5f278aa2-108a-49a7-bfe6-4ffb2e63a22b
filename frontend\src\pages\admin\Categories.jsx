import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { categoriesAPI } from '../../services/api';

const AdminCategories = () => {
  const navigate = useNavigate();
  
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Form state for adding/editing categories
  const [formMode, setFormMode] = useState('add'); // 'add' or 'edit'
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
    image: null,
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await categoriesAPI.getAll();
        setCategories(response.data.results || []);
        
      } catch (err) {
        setError('Failed to load categories');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (type === 'file') {
      setFormData(prev => ({ ...prev, [name]: files[0] }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
    
    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  // Validate form
  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Category name is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      try {
        setSubmitting(true);
        
        // Prepare form data for API
        const categoryData = new FormData();
        categoryData.append('name', formData.name);
        categoryData.append('description', formData.description);
        categoryData.append('is_active', formData.is_active);
        
        if (formData.image) {
          categoryData.append('image', formData.image);
        }
        
        if (formMode === 'add') {
          // Create new category
          await categoriesAPI.create(categoryData);
        } else {
          // Update existing category
          await categoriesAPI.update(selectedCategory.id, categoryData);
        }
        
        // Refresh categories list
        const response = await categoriesAPI.getAll();
        setCategories(response.data.results || []);
        
        // Reset form
        resetForm();
        
      } catch (err) {
        console.error('Error saving category:', err);
        setFormErrors({ general: 'Failed to save category' });
      } finally {
        setSubmitting(false);
      }
    }
  };
  
  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      is_active: true,
      image: null,
    });
    setFormErrors({});
    setSelectedCategory(null);
    setFormMode('add');
    setShowForm(false);
  };
  
  // Handle edit category
  const handleEditCategory = (category) => {
    setSelectedCategory(category);
    setFormData({
      name: category.name,
      description: category.description || '',
      is_active: category.is_active,
      image: null, // Can't pre-fill file input
    });
    setFormMode('edit');
    setShowForm(true);
  };
  
  // Handle delete category
  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await categoriesAPI.delete(categoryId);
        
        // Refresh categories list
        const response = await categoriesAPI.getAll();
        setCategories(response.data.results || []);
        
      } catch (err) {
        console.error('Error deleting category:', err);
        alert('Failed to delete category');
      }
    }
  };
  
  return (
    <div className="admin-categories-page">
      <div className="admin-header">
        <h1>Categories</h1>
        <button 
          className="btn btn-primary"
          onClick={() => {
            resetForm();
            setShowForm(true);
          }}
        >
          <i className="fas fa-plus"></i> Add New Category
        </button>
      </div>
      
      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading categories...</p>
        </div>
      ) : error ? (
        <div className="error-container">
          <p>{error}</p>
          <button onClick={() => window.location.reload()} className="btn btn-primary">
            Try Again
          </button>
        </div>
      ) : (
        <div className="admin-content">
          {showForm && (
            <div className="admin-form-container">
              <div className="admin-form-header">
                <h2>{formMode === 'add' ? 'Add New Category' : 'Edit Category'}</h2>
                <button 
                  className="btn-icon close-btn"
                  onClick={resetForm}
                  aria-label="Close form"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              
              {formErrors.general && (
                <div className="form-error general-error">
                  {formErrors.general}
                </div>
              )}
              
              <form className="admin-form" onSubmit={handleSubmit}>
                <div className="form-group">
                  <label htmlFor="name" className="form-label">Category Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className={`form-control ${formErrors.name ? 'error' : ''}`}
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter category name"
                  />
                  {formErrors.name && <div className="form-error">{formErrors.name}</div>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="description" className="form-label">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    className="form-control"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Enter category description"
                    rows="3"
                  ></textarea>
                </div>
                
                <div className="form-group">
                  <label htmlFor="image" className="form-label">Category Image</label>
                  <input
                    type="file"
                    id="image"
                    name="image"
                    className="form-control"
                    onChange={handleChange}
                    accept="image/*"
                  />
                  {formMode === 'edit' && selectedCategory.image && (
                    <div className="current-image">
                      <p>Current image:</p>
                      <img 
                        src={selectedCategory.image} 
                        alt={selectedCategory.name} 
                        className="thumbnail"
                      />
                    </div>
                  )}
                </div>
                
                <div className="form-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleChange}
                    />
                    Active
                  </label>
                </div>
                
                <div className="form-actions">
                  <button 
                    type="submit" 
                    className="btn btn-primary"
                    disabled={submitting}
                  >
                    {submitting ? 'Saving...' : formMode === 'add' ? 'Add Category' : 'Update Category'}
                  </button>
                  <button 
                    type="button" 
                    className="btn btn-outline"
                    onClick={resetForm}
                    disabled={submitting}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}
          
          {categories.length === 0 ? (
            <div className="empty-state">
              <p>No categories found.</p>
              <button 
                onClick={() => {
                  resetForm();
                  setShowForm(true);
                }} 
                className="btn btn-primary"
              >
                Add Your First Category
              </button>
            </div>
          ) : (
            <div className="categories-grid">
              {categories.map(category => (
                <div key={category.id} className="category-card">
                  <div className="category-image">
                    {category.image ? (
                      <img 
                        src={category.image} 
                        alt={category.name} 
                      />
                    ) : (
                      <div className="category-image-placeholder">
                        No Image
                      </div>
                    )}
                  </div>
                  <div className="category-details">
                    <h3 className="category-name">{category.name}</h3>
                    {category.description && (
                      <p className="category-description">{category.description}</p>
                    )}
                    <div className="category-meta">
                      <span className={`status-badge ${category.is_active ? 'status-active' : 'status-inactive'}`}>
                        {category.is_active ? 'Active' : 'Inactive'}
                      </span>
                      <span className="product-count">
                        {category.products_count} products
                      </span>
                    </div>
                  </div>
                  <div className="category-actions">
                    <button 
                      className="btn-icon"
                      onClick={() => handleEditCategory(category)}
                      aria-label="Edit category"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button 
                      className="btn-icon"
                      onClick={() => handleDeleteCategory(category.id)}
                      aria-label="Delete category"
                    >
                      <i className="fas fa-trash-alt"></i>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdminCategories;
