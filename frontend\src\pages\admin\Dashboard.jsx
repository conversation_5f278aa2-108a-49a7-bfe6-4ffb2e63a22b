import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { productsAPI, categoriesAPI, ordersAPI } from '../../services/api';

const AdminDashboard = () => {
  const navigate = useNavigate();
  
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCategories: 0,
    totalOrders: 0,
    pendingOrders: 0,
    recentOrders: [],
    lowStockProducts: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch products
        const productsResponse = await productsAPI.getAll();
        const products = productsResponse.data.results || [];
        
        // Fetch categories
        const categoriesResponse = await categoriesAPI.getAll();
        const categories = categoriesResponse.data.results || [];
        
        // Fetch orders
        const ordersResponse = await ordersAPI.getAll();
        const orders = ordersResponse.data.results || [];
        
        // Calculate stats
        const pendingOrders = orders.filter(order => 
          order.status === 'pending' || order.status === 'processing'
        );
        
        const lowStockProducts = products.filter(product => 
          product.is_available && product.stock <= 5
        );
        
        setStats({
          totalProducts: products.length,
          totalCategories: categories.length,
          totalOrders: orders.length,
          pendingOrders: pendingOrders.length,
          recentOrders: orders.slice(0, 5),
          lowStockProducts: lowStockProducts.slice(0, 5),
        });
        
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, []);
  
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()} className="btn btn-primary">
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <h1>Admin Dashboard</h1>
      </div>
      
      <div className="admin-stats">
        <div className="stat-card">
          <div className="stat-icon">
            <i className="fas fa-box"></i>
          </div>
          <div className="stat-content">
            <h3>Total Products</h3>
            <p className="stat-value">{stats.totalProducts}</p>
            <Link to="/admin/products" className="stat-link">View All</Link>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <i className="fas fa-tags"></i>
          </div>
          <div className="stat-content">
            <h3>Categories</h3>
            <p className="stat-value">{stats.totalCategories}</p>
            <Link to="/admin/categories" className="stat-link">View All</Link>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <i className="fas fa-shopping-bag"></i>
          </div>
          <div className="stat-content">
            <h3>Total Orders</h3>
            <p className="stat-value">{stats.totalOrders}</p>
            <Link to="/admin/orders" className="stat-link">View All</Link>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <i className="fas fa-clock"></i>
          </div>
          <div className="stat-content">
            <h3>Pending Orders</h3>
            <p className="stat-value">{stats.pendingOrders}</p>
            <Link to="/admin/orders?status=pending" className="stat-link">View All</Link>
          </div>
        </div>
      </div>
      
      <div className="admin-sections">
        <div className="admin-section">
          <div className="section-header">
            <h2>Recent Orders</h2>
            <Link to="/admin/orders" className="view-all">View All</Link>
          </div>
          
          {stats.recentOrders.length > 0 ? (
            <div className="table-responsive">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Total</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.recentOrders.map(order => (
                    <tr key={order.id}>
                      <td>#{order.id}</td>
                      <td>{order.full_name}</td>
                      <td>{new Date(order.created_at).toLocaleDateString()}</td>
                      <td>
                        <span className={`status-badge status-${order.status}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </td>
                      <td>{order.total_price} DT</td>
                      <td>
                        <button 
                          className="btn-icon"
                          onClick={() => navigate(`/admin/orders/${order.id}`)}
                          aria-label="View order"
                        >
                          <i className="fas fa-eye"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="no-data">No recent orders</p>
          )}
        </div>
        
        <div className="admin-section">
          <div className="section-header">
            <h2>Low Stock Products</h2>
            <Link to="/admin/products" className="view-all">View All</Link>
          </div>
          
          {stats.lowStockProducts.length > 0 ? (
            <div className="table-responsive">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Category</th>
                    <th>Price</th>
                    <th>Stock</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.lowStockProducts.map(product => (
                    <tr key={product.id}>
                      <td>{product.name}</td>
                      <td>{product.category_name}</td>
                      <td>{product.price} DT</td>
                      <td>
                        <span className={`stock-badge ${product.stock <= 2 ? 'critical' : 'low'}`}>
                          {product.stock}
                        </span>
                      </td>
                      <td>
                        <button 
                          className="btn-icon"
                          onClick={() => navigate(`/admin/products/edit/${product.id}`)}
                          aria-label="Edit product"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="no-data">No low stock products</p>
          )}
        </div>
      </div>
      
      <div className="admin-actions">
        <div className="action-card">
          <div className="action-icon">
            <i className="fas fa-plus"></i>
          </div>
          <h3>Add New Product</h3>
          <p>Add a new product to your store</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/admin/products/new')}
          >
            Add Product
          </button>
        </div>
        
        <div className="action-card">
          <div className="action-icon">
            <i className="fas fa-folder-plus"></i>
          </div>
          <h3>Add New Category</h3>
          <p>Create a new product category</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/admin/categories/new')}
          >
            Add Category
          </button>
        </div>
        
        <div className="action-card">
          <div className="action-icon">
            <i className="fas fa-cog"></i>
          </div>
          <h3>Site Settings</h3>
          <p>Manage website settings</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/admin/settings')}
          >
            Manage Settings
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
