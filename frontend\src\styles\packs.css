/* PACKS PAGE STYLES */
.packs-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
  background: var(--gray-light);
  min-height: 100vh;
}

/* Page Header */
.packs-header {
  margin-bottom: var(--space-8);
  text-align: center;
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.packs-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--space-3);
  position: relative;
  display: inline-block;
  padding-bottom: var(--space-3);
}

.packs-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.packs-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Packs Container */
.packs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Packs Grid - Match Product Grid Size */
.packs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-4);
}

/* Responsive Pack Grid - Match Product Grid */
@media (max-width: 1024px) {
  .packs-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .packs-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-3);
  }
}

/* Pack Card - Creative Compact Design */
.pack-card {
  background: var(--white);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 2px solid transparent;
  position: relative;
  background: linear-gradient(var(--white), var(--white)) padding-box,
              linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(220, 38, 38, 0.05)) border-box;
}

.pack-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 8px 30px rgba(220, 38, 38, 0.15);
  border-color: rgba(220, 38, 38, 0.3);
}

.pack-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--red), var(--red-dark), #ff6b6b);
  border-radius: 22px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pack-card:hover::before {
  opacity: 1;
}

.pack-card-link {
  text-decoration: none;
  color: inherit;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Pack Image - Ultra Compact Design */
.pack-image-container {
  position: relative;
  height: 80px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

.pack-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.pack-card:hover .pack-image {
  transform: scale(1.08);
}

.pack-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  position: relative;
}

.pack-image-placeholder svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.pack-card:hover .pack-image-placeholder svg {
  transform: scale(1.1);
}

/* Discount Badge */
.pack-discount-badge {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: var(--white);
  padding: var(--space-2) var(--space-4);
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: 0 4px 16px rgba(255, 71, 87, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pack-discount-badge svg {
  width: 12px;
  height: 12px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* Pack Info - Ultra Compact Layout */
.pack-info {
  padding: var(--space-1) var(--space-3);
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--white);
  margin-top: -6px;
  position: relative;
  z-index: 1;
}

.pack-name {
  font-size: 1rem;
  font-weight: 800;
  color: var(--black);
  margin-bottom: 2px;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pack-description {
  color: var(--gray);
  font-size: 0.75rem;
  line-height: 1.3;
  margin-bottom: var(--space-1);
  font-weight: 400;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Pack Products - Ultra Compact */
.pack-products {
  margin-bottom: var(--space-1);
  background: rgba(220, 38, 38, 0.03);
  border-radius: 6px;
  padding: 4px var(--space-2);
  border: 1px solid rgba(220, 38, 38, 0.08);
}

.pack-products-label {
  font-size: 0.7rem;
  font-weight: 700;
  color: var(--red);
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.pack-products-label::before {
  content: '🎁';
  margin-right: 4px;
  font-size: 0.7rem;
}

.pack-products-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pack-products-list li {
  font-size: 0.7rem;
  color: var(--gray);
  padding: 2px var(--space-1);
  background: var(--white);
  border-radius: 4px;
  border-left: 2px solid var(--red);
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pack-products-list li:hover {
  background: rgba(220, 38, 38, 0.05);
  transform: translateX(1px);
}

.pack-products-list li::before {
  content: '•';
  color: var(--red);
  font-weight: 900;
  margin-right: 4px;
}

/* Pack Pricing - Compact Version */
.pack-pricing {
  margin-top: auto;
  background: rgba(220, 38, 38, 0.03);
  border-radius: 8px;
  padding: var(--space-2);
  border: 1px solid rgba(220, 38, 38, 0.08);
}

.pack-prices {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
  margin-bottom: var(--space-1);
}

.pack-original-price {
  font-size: 0.85rem;
  color: var(--black);
  text-decoration: line-through;
  font-weight: 600;
  opacity: 0.7;
}

.pack-current-price {
  font-size: 1.2rem;
  font-weight: 800;
  color: var(--red);
}

.pack-savings {
  font-size: 0.75rem;
  color: #28a745;
  font-weight: 600;
  background: rgba(40, 167, 69, 0.1);
  padding: 2px var(--space-2);
  border-radius: 12px;
  border: 1px solid rgba(40, 167, 69, 0.15);
  display: inline-flex;
  align-items: center;
  gap: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.pack-savings::before {
  content: '💰';
  font-size: 0.7rem;
}

/* Add to Cart Button - Ultra Compact */
.pack-add-to-cart-btn {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border: none;
  font-size: 0.8rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  border-radius: 0 0 20px 20px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

.pack-add-to-cart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.pack-add-to-cart-btn:hover::before {
  left: 100%;
}

.pack-add-to-cart-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--red-dark) 0%, #c53030 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.pack-add-to-cart-btn:active {
  transform: translateY(0);
}

.pack-add-to-cart-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--gray-light);
}

.pack-add-to-cart-btn svg {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
}

.pack-add-to-cart-btn:hover svg {
  transform: scale(1.1);
}

/* Loading and Error States */
.loading-container,
.error-message,
.no-packs {
  text-align: center;
  padding: var(--space-8);
  max-width: 600px;
  margin: 0 auto;
}

.loading-spinner {
  font-size: 2rem;
  color: var(--red);
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-container h2,
.error-message h3,
.no-packs h3 {
  color: var(--gray);
  margin-bottom: var(--space-3);
}

.loading-container p,
.error-message p,
.no-packs p {
  color: var(--gray-light);
  margin-bottom: var(--space-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .packs-header {
    padding: var(--space-6) var(--space-4);
    border-radius: 8px;
  }

  .packs-header h1 {
    font-size: 2rem;
  }

  .packs-header p {
    font-size: 1rem;
  }

  .packs-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .pack-card {
    margin: 0 var(--space-2);
  }

  .pack-info {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .packs-page {
    padding: var(--space-4) 0;
  }

  .packs-header {
    padding: var(--space-4) var(--space-3);
    margin-bottom: var(--space-6);
    border-radius: 6px;
  }

  .packs-header h1 {
    font-size: 1.8rem;
  }

  .packs-header h1::after {
    width: 60px;
    height: 2px;
  }

  .packs-header p {
    font-size: 0.9rem;
  }

  .pack-image-container {
    height: 160px;
  }

  .pack-info {
    padding: var(--space-3);
  }

  .pack-name {
    font-size: 1.1rem;
  }
}
