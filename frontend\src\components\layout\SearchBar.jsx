import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { productsAPI } from '../../services/api';

const SearchBar = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchRef = useRef(null);
  const navigate = useNavigate();
  
  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    if (value.trim().length > 2) {
      performSearch(value);
    } else {
      setSearchResults([]);
    }
  };
  
  // Debounced search function
  const performSearch = async (term) => {
    setIsLoading(true);
    try {
      const response = await productsAPI.search(term);
      setSearchResults(response.data.results || []);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle search form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchTerm.trim())}`);
      setShowResults(false);
    }
  };
  
  // Handle clicking on a search result
  const handleResultClick = (productId) => {
    navigate(`/products/${productId}`);
    setSearchTerm('');
    setSearchResults([]);
    setShowResults(false);
  };
  
  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className="search-container" ref={searchRef}>
      <form onSubmit={handleSubmit} className="search-form">
        <input
          type="text"
          placeholder="Search products..."
          value={searchTerm}
          onChange={handleSearchChange}
          onFocus={() => setShowResults(true)}
          className="search-input"
        />
        <button type="submit" className="search-button">
          <i className="fas fa-search"></i>
        </button>
        
        {isLoading && (
          <div className="search-loading">
            <div className="search-spinner"></div>
          </div>
        )}
      </form>
      
      {showResults && searchTerm.trim().length > 2 && (
        <div className="search-results">
          {searchResults.length > 0 ? (
            <>
              <div className="search-results-list">
                {searchResults.slice(0, 5).map(product => (
                  <div 
                    key={product.id} 
                    className="search-result-item"
                    onClick={() => handleResultClick(product.id)}
                  >
                    <div className="search-result-image">
                      {product.images && product.images.length > 0 ? (
                        <img src={product.images[0].image} alt={product.name} />
                      ) : (
                        <div className="search-result-no-image">
                          <i className="fas fa-image"></i>
                        </div>
                      )}
                    </div>
                    <div className="search-result-info">
                      <div className="search-result-name">{product.name}</div>
                      <div className="search-result-price">{product.price} DT</div>
                    </div>
                  </div>
                ))}
              </div>
              
              {searchResults.length > 5 && (
                <div 
                  className="search-view-all"
                  onClick={() => {
                    navigate(`/products?search=${encodeURIComponent(searchTerm.trim())}`);
                    setShowResults(false);
                  }}
                >
                  View all {searchResults.length} results
                </div>
              )}
            </>
          ) : (
            <div className="search-no-results">
              No products found for "{searchTerm}"
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
