/* CONTACT US PAGE - CLEAN & SIMPLE */

/* Page Container */
.contact-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
  background: var(--gray-light);
  min-height: 100vh;
}

/* Page Header */
.contact-page-header {
  margin-bottom: var(--space-8);
  text-align: center;
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.contact-page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--space-3);
  position: relative;
  display: inline-block;
  padding-bottom: var(--space-3);
}

.contact-page-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.contact-page-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.contact-page-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.contact-page-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
}

/* Contact Container */
.contact-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

/* Contact Info */
.contact-info {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  box-shadow: var(--shadow);
}

.contact-info h2 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  position: relative;
  padding-bottom: var(--space-2);
}

.contact-info h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--red);
  border-radius: 2px;
}

.contact-info > p {
  color: var(--gray);
  line-height: 1.7;
  margin-bottom: var(--space-6);
  font-size: 1.1rem;
}

/* Contact Methods */
.contact-methods {
  margin-bottom: var(--space-8);
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  border-radius: 8px;
  transition: background 0.2s;
}

.contact-method:hover {
  background: var(--gray-light);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: var(--red);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon i {
  font-size: 1.2rem;
  color: var(--white);
}

.contact-details h3 {
  font-size: 1.2rem;
  color: var(--black);
  margin-bottom: var(--space-2);
}

.contact-details p {
  color: var(--gray);
  margin: 0;
  line-height: 1.5;
}

/* Social Links in Contact */
.contact-info .social-links h3 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-4);
}

.social-icons {
  display: flex;
  gap: var(--space-3);
}

.social-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.2s;
  border: 2px solid var(--border);
}

.social-icon i {
  font-size: 1.2rem;
}

.social-icon.facebook {
  color: #1877f2;
  border-color: #1877f2;
}

.social-icon.facebook:hover {
  background: #1877f2;
  color: var(--white);
}

.social-icon.instagram {
  color: #e6683c;
  border-color: #e6683c;
}

.social-icon.instagram:hover {
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743);
  color: var(--white);
}

.social-icon.tiktok {
  color: var(--black);
  border-color: var(--black);
}

.social-icon.tiktok:hover {
  background: var(--black);
  color: var(--white);
}

/* Contact Form Container */
.contact-form-container {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  box-shadow: var(--shadow);
}

.contact-form-container h2 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: var(--space-6);
  position: relative;
  padding-bottom: var(--space-2);
}

.contact-form-container h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--red);
  border-radius: 2px;
}

/* Contact Form */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: var(--black);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: var(--space-3);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #dc2626;
}

.form-error {
  color: #dc2626;
  font-size: 0.8rem;
  margin-top: var(--space-1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Success Message */
.success-message {
  text-align: center;
  padding: var(--space-8);
}

.success-message i {
  font-size: 4rem;
  color: #10b981;
  margin-bottom: var(--space-4);
}

.success-message h3 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: var(--space-4);
}

.success-message p {
  color: var(--gray);
  font-size: 1.1rem;
  margin-bottom: var(--space-6);
}

/* Error Container */
.error-container {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: var(--space-3);
  margin-bottom: var(--space-4);
}

.error-container p {
  color: #dc2626;
  margin: 0;
  font-size: 0.9rem;
}

/* Spinner */
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--white);
  animation: spin 1s ease-in-out infinite;
  margin-right: var(--space-2);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-page {
    padding: var(--space-3);
  }

  .contact-page-header {
    padding: var(--space-6) var(--space-4);
    border-radius: 8px;
  }

  .contact-page-header h1 {
    font-size: 2rem;
  }

  .contact-page-header p {
    font-size: 1rem;
  }

  .contact-section {
    padding: var(--space-8) var(--space-4);
  }

  .contact-container {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .contact-form {
    order: 1;
  }

  .contact-info {
    order: 2;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .form-group input,
  .form-group textarea {
    padding: var(--space-3);
    font-size: 1rem;
  }

  .submit-btn {
    width: 100%;
    padding: var(--space-4);
    font-size: 1.1rem;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .contact-method-icon {
    margin-bottom: var(--space-3);
  }

  .contact-method h3 {
    font-size: 1.3rem;
    margin-bottom: var(--space-2);
  }

  .contact-method p {
    font-size: 1rem;
  }

  .social-icons {
    justify-content: center;
    gap: var(--space-3);
  }

  .social-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-page {
    padding: var(--space-2);
  }

  .contact-page-header {
    padding: var(--space-4) var(--space-3);
    border-radius: 6px;
  }

  .contact-page-header h1 {
    font-size: 1.8rem;
  }

  .contact-page-header h1::after {
    width: 60px;
    height: 2px;
  }

  .contact-page-header p {
    font-size: 0.9rem;
  }

  .contact-section {
    padding: var(--space-6) var(--space-3);
  }

  .contact-container {
    gap: var(--space-4);
  }

  .form-group input,
  .form-group textarea {
    padding: var(--space-3);
    font-size: 0.9rem;
  }

  .submit-btn {
    padding: var(--space-3);
    font-size: 1rem;
  }

  .contact-method {
    padding: var(--space-3);
  }

  .contact-method h3 {
    font-size: 1.2rem;
  }

  .contact-method p {
    font-size: 0.9rem;
  }

  .social-icon {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }
}
