/* CART PAGE - ENHANCED DESIGN */

/* Page Container */
.cart-page {
  min-height: 100vh;
  padding: var(--space-6) var(--space-4);
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  position: relative;
}

.cart-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.03) 0%, rgba(220, 38, 38, 0.01) 100%);
  border-radius: 0 0 50px 50px;
  z-index: 0;
}

.cart-page > * {
  position: relative;
  z-index: 1;
}

/* Cart Header - Red Style (matching other pages) */
.cart-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6) var(--space-4);
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  color: var(--white);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
  position: relative;
  overflow: hidden;
}

.cart-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.cart-header h1 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: var(--space-3);
  color: var(--white);
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}



.cart-header p {
  font-size: 1.1rem;
  color: var(--white);
  font-weight: 400;
  margin: 0;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

/* Guest Cart Notice */
.guest-cart-notice {
  background: var(--gray-light);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-4);
  margin-top: var(--space-4);
  text-align: center;
}

.guest-cart-notice p {
  margin: 0;
  color: var(--gray);
}

.login-link {
  color: var(--red);
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}

/* Empty Cart - Enhanced Modern Design */
.empty-cart {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  padding: var(--space-8);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.empty-cart-content {
  text-align: center;
  max-width: 800px;
  width: 100%;
  padding: var(--space-12);
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  border: 1px solid rgba(220, 38, 38, 0.08);
  border-radius: 32px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.08),
    0 8px 25px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.empty-cart-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, var(--red) 0%, #dc2626 50%, var(--red) 100%);
}

/* Illustration Section */
.empty-cart-illustration {
  position: relative;
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-cart-icon {
  font-size: 6rem;
  color: var(--red);
  opacity: 0.9;
  animation: float 4s ease-in-out infinite;
  z-index: 2;
  position: relative;
}



@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-15px) scale(1.05); }
}



/* Text Section */
.empty-cart-text {
  margin-bottom: var(--space-8);
}

.empty-cart-text h2 {
  font-size: 2.5rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  font-weight: 700;
  line-height: 1.2;
  background: linear-gradient(135deg, var(--black) 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-cart-text p {
  color: #6b7280;
  font-size: 1.2rem;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

/* Action Buttons */
.empty-cart-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  margin-bottom: var(--space-10);
  flex-wrap: wrap;
}

.empty-cart-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-8);
  font-weight: 600;
  border-radius: 16px;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 1.1rem;
  min-width: 180px;
  justify-content: center;
}

.empty-cart-actions .btn-primary {
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  border: none;
  color: var(--white);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.empty-cart-actions .btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(220, 38, 38, 0.4);
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.empty-cart-actions .btn-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid rgba(220, 38, 38, 0.15);
  color: var(--red);
}

.empty-cart-actions .btn-secondary:hover {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: var(--red);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.2);
}

/* Features Section */
.empty-cart-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  padding: var(--space-6) 0;
  border-top: 1px solid rgba(220, 38, 38, 0.1);
  border-bottom: 1px solid rgba(220, 38, 38, 0.1);
}

.empty-cart-features .feature {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: 12px;
  background: linear-gradient(135deg, #fefefe 0%, #f9fafb 100%);
  transition: all 0.3s ease;
}

.empty-cart-features .feature:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.empty-cart-features .feature-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
  border-radius: 12px;
  flex-shrink: 0;
}

.empty-cart-features .feature-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 var(--space-1) 0;
}

.empty-cart-features .feature-content p {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Category Suggestions */
.empty-cart-suggestions {
  text-align: center;
}

.empty-cart-suggestions h3 {
  font-size: 1.5rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  font-weight: 600;
}

.category-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--space-3);
  max-width: 600px;
  margin: 0 auto;
}

.category-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(220, 38, 38, 0.1);
  border-radius: 16px;
  text-decoration: none;
  color: var(--black);
  transition: all 0.3s ease;
  font-weight: 500;
}

.category-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15);
  border-color: var(--red);
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.category-link span:first-child {
  font-size: 2rem;
}

.category-link span:last-child {
  font-size: 0.9rem;
}

/* Empty Cart Responsive */
@media (max-width: 1024px) {
  .empty-cart-content {
    max-width: 700px;
    padding: var(--space-10);
  }

  .empty-cart-features {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .category-links {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

@media (max-width: 768px) {
  .empty-cart {
    min-height: 60vh;
    padding: var(--space-4);
  }

  .empty-cart-content {
    padding: var(--space-8);
    border-radius: 24px;
    max-width: 600px;
  }

  .empty-cart-icon {
    font-size: 5rem;
  }

  .empty-cart-text h2 {
    font-size: 2rem;
    margin-bottom: var(--space-3);
  }

  .empty-cart-text p {
    font-size: 1.1rem;
  }

  .empty-cart-actions {
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-8);
  }

  .empty-cart-actions .btn {
    width: 100%;
    min-width: auto;
    padding: var(--space-4) var(--space-6);
  }

  .empty-cart-features {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .empty-cart-features .feature {
    padding: var(--space-4);
  }

  .category-links {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }

  .category-link {
    padding: var(--space-3);
  }

  .category-link span:first-child {
    font-size: 1.5rem;
  }

  .category-link span:last-child {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .empty-cart {
    min-height: 50vh;
    padding: var(--space-3);
  }

  .empty-cart-content {
    padding: var(--space-6);
    border-radius: 20px;
  }

  .empty-cart-icon {
    font-size: 4rem;
  }

  .empty-cart-text h2 {
    font-size: 1.7rem;
    line-height: 1.3;
  }

  .empty-cart-text p {
    font-size: 1rem;
  }

  .empty-cart-actions .btn {
    font-size: 1rem;
    padding: var(--space-3) var(--space-5);
  }

  .empty-cart-features .feature {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
    padding: var(--space-3);
  }

  .empty-cart-features .feature-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }

  .empty-cart-features .feature-content h4 {
    font-size: 0.9rem;
  }

  .empty-cart-features .feature-content p {
    font-size: 0.8rem;
  }

  .empty-cart-suggestions h3 {
    font-size: 1.3rem;
  }

  .category-links {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }

  .category-link {
    padding: var(--space-2);
  }

  .category-link span:first-child {
    font-size: 1.3rem;
  }

  .category-link span:last-child {
    font-size: 0.75rem;
  }
}

@media (max-width: 320px) {
  .empty-cart-content {
    padding: var(--space-4);
  }

  .empty-cart-text h2 {
    font-size: 1.5rem;
  }

  .empty-cart-text p {
    font-size: 0.9rem;
  }

  .category-links {
    grid-template-columns: 1fr;
  }
}

/* Cart Container - Simple & Clean */
.cart-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Cart Items Section */
.cart-items {
  background: transparent;
  padding: 0;
}

/* Enhanced Cart Items Header */
.cart-items-header {
  display: grid;
  grid-template-columns: 2fr 120px 120px 120px 80px;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-5);
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  color: var(--white);
  font-weight: 700;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 16px 16px 0 0;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
}

.cart-items-header > div {
  text-align: center;
}

.cart-items-header > div:first-child {
  text-align: left;
}

/* Grid-Based Cart Item Layout: Product | Price | Quantity | Subtotal | Action */
.cart-item {
  display: grid;
  grid-template-columns: 2fr 120px 120px 120px 80px;
  gap: var(--space-4);
  align-items: center;
  padding: var(--space-4) var(--space-5);
  background: var(--white);
  border-bottom: 1px solid rgba(220, 38, 38, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.cart-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  transition: width 0.3s ease;
}

.cart-item:hover {
  background: rgba(220, 38, 38, 0.02);
  transform: translateX(4px);
}

.cart-item:hover::before {
  width: 4px;
}

.cart-item:last-child {
  border-bottom: none;
  border-radius: 0 0 16px 16px;
}

/* Grid Product Section */
.cart-item-product {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-width: 0;
}

.cart-item-image {
  width: 70px;
  height: 70px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid rgba(220, 38, 38, 0.1);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.cart-item-image:hover {
  transform: scale(1.05);
  border-color: rgba(220, 38, 38, 0.3);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray);
  font-size: 1.5rem;
  font-weight: 600;
}

.pack-placeholder {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  font-size: 1.8rem;
}



/* Pack-specific cart item styles */
.cart-item-pack {
  background: rgba(220, 38, 38, 0.05);
  border-left: 4px solid var(--red);
}

.cart-item-image-placeholder.pack-placeholder {
  background: var(--red);
  color: var(--white);
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.pack-badge {
  background: var(--red);
  color: var(--white);
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: var(--space-2);
  font-weight: 600;
  text-transform: uppercase;
}

/* Pack item name styling */
.cart-item-pack .cart-item-name {
  font-weight: 600;
  color: var(--red);
}

.cart-item-pack .cart-item-category {
  color: var(--gray);
  font-weight: 500;
  font-size: 0.9rem;
}

.cart-item-details {
  flex: 1;
  min-width: 0;
}

.cart-item-name {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: var(--space-1);
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.cart-item-name a {
  color: var(--black);
  text-decoration: none;
  transition: color 0.3s ease;
}

.cart-item-name a:hover {
  color: var(--red);
}

.pack-badge {
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  color: var(--white);
  font-size: 0.6rem;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.3);
}

.cart-item-category {
  font-size: 0.8rem;
  color: var(--gray);
  margin-bottom: var(--space-1);
  font-weight: 500;
}

/* Compact Pack Contents for Grid Layout */
.pack-contents {
  margin-top: var(--space-1);
  padding: var(--space-2);
  background: rgba(220, 38, 38, 0.05);
  border: 1px solid rgba(220, 38, 38, 0.1);
  border-radius: 6px;
  border-left: 2px solid var(--red);
}

.pack-contents-label {
  font-size: 0.7rem;
  font-weight: 700;
  color: var(--red);
  margin-bottom: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pack-contents-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pack-contents-list li {
  font-size: 0.7rem;
  color: var(--black);
  padding: 2px var(--space-1);
  background: var(--white);
  border-radius: 3px;
  border: 1px solid rgba(220, 38, 38, 0.1);
  position: relative;
  padding-left: var(--space-3);
  transition: all 0.2s ease;
}

.pack-contents-list li:hover {
  background: rgba(220, 38, 38, 0.02);
  border-color: rgba(220, 38, 38, 0.2);
}

.pack-contents-list li::before {
  content: '•';
  color: var(--red);
  position: absolute;
  left: var(--space-1);
  font-weight: 700;
  font-size: 0.6rem;
}

/* Grid Price Section */
.cart-item-price-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 60px;
}

.cart-item-price-display .cart-item-price {
  font-size: 1rem;
  font-weight: 700;
  color: var(--red);
  line-height: 1.2;
}

/* Grid Quantity Section */
.cart-item-quantity-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

/* Grid Subtotal Section */
.cart-item-subtotal-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 60px;
}

.cart-item-subtotal-display .cart-item-subtotal {
  font-size: 1rem;
  font-weight: 700;
  color: var(--black);
  line-height: 1.2;
}

/* Grid Actions Section */
.cart-item-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}



.cart-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  transition: width 0.3s ease;
}

.cart-item:hover {
  background: rgba(220, 38, 38, 0.02);
  transform: translateX(4px);
}

.cart-item:hover::before {
  width: 4px;
}

.cart-item:last-child {
  border-bottom: none;
  border-radius: 0 0 16px 16px;
}

/* Grid Product Section */
.cart-item-product {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-width: 0;
}

.cart-item-image {
  width: 70px;
  height: 70px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid rgba(220, 38, 38, 0.1);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.cart-item-image:hover {
  transform: scale(1.05);
  border-color: rgba(220, 38, 38, 0.3);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray);
  font-size: 1.5rem;
  font-weight: 600;
}

.pack-placeholder {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  font-size: 1.8rem;
}

.cart-item-details {
  flex: 1;
  min-width: 0;
}

.cart-item-name {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: var(--space-1);
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.cart-item-name a {
  color: var(--black);
  text-decoration: none;
  transition: color 0.3s ease;
}

.cart-item-name a:hover {
  color: var(--red);
}

.pack-badge {
  background: linear-gradient(135deg, var(--red) 0%, #dc2626 100%);
  color: var(--white);
  font-size: 0.6rem;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.3);
}

.cart-item-category {
  font-size: 0.8rem;
  color: var(--gray);
  margin-bottom: var(--space-1);
  font-weight: 500;
}

/* Grid Price Section */
.cart-item-price-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 60px;
}

.cart-item-price-display .cart-item-price {
  font-size: 1rem;
  font-weight: 700;
  color: var(--red);
  line-height: 1.2;
}

/* Grid Quantity Section */
.cart-item-quantity-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

/* Grid Actions Section */
.cart-item-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

/* Remove old footer styles - now using single row layout */

.cart-item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border);
  flex-shrink: 0;
}

.cart-item-image-placeholder {
  width: 100%;
  height: 100%;
  background: var(--gray-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray);
  font-size: 1.5rem;
}

.pack-placeholder {
  background: #fef3c7;
  color: #d97706;
}

.cart-item-details {
  flex: 1;
  min-width: 0;
}

.cart-item-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--space-1);
  line-height: 1.3;
}

.cart-item-name a {
  color: var(--black);
  text-decoration: none;
}

.cart-item-name a:hover {
  color: var(--red);
}

.pack-badge {
  background: var(--red);
  color: var(--white);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  margin-left: var(--space-2);
}

.cart-item-category {
  font-size: 0.85rem;
  color: var(--gray);
  margin: 0;
}
}

.cart-item-category {
  color: var(--gray);
  font-size: 0.9rem;
  margin: 0;
}

/* Cart Item Price & Total */
.cart-item-price,
.cart-item-total {
  font-weight: 600;
  color: var(--red);
  font-size: 1.1rem;
}

/* Compact Quantity Selector for Grid */
.quantity-selector {
  display: flex;
  align-items: center;
  gap: 0;
  background: var(--white);
  border: 1px solid rgba(220, 38, 38, 0.2);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin: 0;
  padding: 0;
}

.quantity-selector:hover {
  border-color: rgba(220, 38, 38, 0.4);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.15);
}

/* Ensure form styling doesn't interfere */
.quantity-selector form {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  gap: 0;
  width: 100%;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: var(--gray-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--gray);
  font-size: 0.8rem;
  font-weight: 600;
}

.quantity-btn:hover:not(:disabled) {
  background: var(--red);
  color: var(--white);
  transform: scale(1.05);
}

.quantity-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: #e9ecef;
}

.quantity-btn:active:not(:disabled) {
  transform: scale(0.95);
}

.quantity-selector input {
  width: 40px;
  height: 30px;
  text-align: center;
  border: none;
  background: var(--white);
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--black);
  padding: 0;
  outline: none;
  border-left: 1px solid rgba(220, 38, 38, 0.2);
  border-right: 1px solid rgba(220, 38, 38, 0.2);
}

.quantity-selector input:focus {
  background: rgba(220, 38, 38, 0.05);
  color: var(--red);
}

.quantity-selector input::-webkit-outer-spin-button,
.quantity-selector input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity-selector input[type=number] {
  -moz-appearance: textfield;
}

.quantity-selector input:focus {
  background: var(--gray-light);
}

/* Remove Item Button */
.remove-item-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-item-btn:hover {
  background: #dc2626;
  color: var(--white);
}

/* Compact Remove Button for Grid */
.remove-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--red);
  color: var(--white);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  box-shadow: 0 2px 6px rgba(220, 38, 38, 0.3);
}

.remove-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

.remove-btn:active {
  transform: scale(0.95);
}

/* Cart Summary - Enhanced */
.cart-summary {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-4);
  height: fit-content;
  position: sticky;
  top: var(--space-4);
}

.cart-summary h2 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  font-weight: 600;
}

.cart-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: 0.95rem;
}

.cart-summary-row.total {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--black);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border);
  margin-top: var(--space-3);
}

.free-delivery {
  color: #10b981;
  font-weight: 600;
}

/* Cart Summary Actions */
.cart-summary-actions {
  margin-top: var(--space-6);
}

.checkout-btn {
  width: 100%;
  margin-bottom: var(--space-3);
  padding: var(--space-3);
  font-size: 1rem;
  font-weight: 600;
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkout-btn:hover {
  background: var(--red-dark);
}

.cart-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.cart-buttons .btn {
  text-align: center;
  padding: var(--space-3);
}

/* Cart Notes */
.cart-notes {
  margin-top: var(--space-4);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border);
}

.cart-notes p {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  color: var(--gray);
  font-size: 0.8rem;
}

.cart-notes p:last-child {
  margin-bottom: 0;
}

.cart-notes svg {
  color: var(--red);
  font-size: 0.9rem;
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-light);
  border-top: 4px solid var(--red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design - Enhanced */
@media (max-width: 1024px) {
  .cart-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .cart-summary {
    position: static;
    order: 2;
  }
}

@media (max-width: 768px) {
  .cart-page {
    padding: var(--space-3);
  }

  .cart-page::before {
    height: 150px;
    border-radius: 0 0 30px 30px;
  }

  .cart-header {
    padding: var(--space-4);
    margin-bottom: var(--space-6);
    border-radius: 12px;
  }

  .cart-header h1 {
    font-size: 1.8rem;
    margin-bottom: var(--space-2);
  }

  .cart-header p {
    font-size: 1rem;
  }
    margin-bottom: var(--space-4);
  }

  .cart-items-header {
    display: none;
  }

  .cart-items-header {
    display: none;
  }

  .cart-item {
    grid-template-columns: 1fr;
    gap: var(--space-3);
    padding: var(--space-3);
  }

  .cart-item-product {
    grid-column: 1;
    margin-bottom: var(--space-3);
  }

  .cart-item-image {
    width: 60px;
    height: 60px;
  }

  .cart-item-name {
    font-size: 1rem;
    margin-bottom: var(--space-1);
  }

  /* Mobile: Create a grid for quantity, price, subtotal, and actions */
  .cart-item-quantity-section,
  .cart-item-price-display,
  .cart-item-subtotal-display,
  .cart-item-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) var(--space-3);
    background: rgba(220, 38, 38, 0.05);
    border: 1px solid rgba(220, 38, 38, 0.1);
    border-radius: 8px;
    margin-bottom: var(--space-2);
  }

  .cart-item-quantity-section::before {
    content: 'Quantity:';
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray);
    text-transform: uppercase;
  }

  .cart-item-price-display::before {
    content: 'Price:';
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray);
    text-transform: uppercase;
  }

  .cart-item-subtotal-display::before {
    content: 'Subtotal:';
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray);
    text-transform: uppercase;
  }

  .cart-item-actions::before {
    content: 'Action:';
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray);
    text-transform: uppercase;
  }

  /* Pack contents adjustments for mobile */
  .pack-contents {
    margin-top: var(--space-2);
    padding: var(--space-2);
  }

  .pack-contents-list li {
    font-size: 0.75rem;
    padding: 2px var(--space-1);
  }

  .cart-item-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--red);
  }

  .quantity-selector {
    justify-content: center;
    margin: var(--space-3) 0;
  }

  .quantity-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .quantity-input {
    width: 60px;
    height: 40px;
    font-size: 1rem;
  }

  .cart-buttons {
    flex-direction: column;
    gap: var(--space-2);
  }

  .cart-summary {
    padding: var(--space-4);
    margin-top: var(--space-4);
  }

  .summary-row {
    font-size: 1rem;
    padding: var(--space-2) 0;
  }

  .summary-total {
    font-size: 1.3rem;
  }

  .checkout-btn {
    width: 100%;
    padding: var(--space-4);
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .cart-page {
    padding: var(--space-2);
  }

  .cart-header h1 {
    font-size: 1.8rem;
  }

  .cart-item {
    padding: var(--space-3);
  }

  .cart-item-image {
    width: 70px;
    height: 70px;
  }

  .cart-item-details h3 {
    font-size: 1rem;
  }

  .cart-item-price {
    font-size: 1.1rem;
  }

  .quantity-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .quantity-input {
    width: 50px;
    height: 35px;
    font-size: 0.9rem;
  }

  .cart-summary {
    padding: var(--space-3);
  }

  .summary-row {
    font-size: 0.9rem;
  }

  .summary-total {
    font-size: 1.2rem;
  }

  .checkout-btn {
    padding: var(--space-3);
    font-size: 1rem;
  }
}
