# Generated by Django 5.2.4 on 2025-07-27 12:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0002_rename_is_active_product_is_available_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Pack',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('slug', models.CharField(blank=True, max_length=50)),
                ('description', models.TextField()),
                ('original_price', models.DecimalField(decimal_places=2, help_text='Sum of individual product prices', max_digits=10)),
                ('pack_price', models.DecimalField(decimal_places=2, help_text='Discounted pack price', max_digits=10)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='packs/')),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PackProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pack', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.pack')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.product')),
            ],
            options={
                'unique_together': {('pack', 'product')},
            },
        ),
        migrations.AddField(
            model_name='pack',
            name='products',
            field=models.ManyToManyField(related_name='packs', through='api.PackProduct', to='api.product'),
        ),
    ]
