from rest_framework import serializers
from django.contrib.auth.models import User
from .models import *


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'


class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = '__all__'


class ProductSerializer(serializers.ModelSerializer):
    images = ProductImageSerializer(many=True, read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    image = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = '__all__'

    def get_image(self, obj):
        # Get the primary image or the first image
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image:
            return primary_image.image.url if primary_image.image else None

        first_image = obj.images.first()
        if first_image:
            return first_image.image.url if first_image.image else None

        return None


class PackProductSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = PackProduct
        fields = ['id', 'product', 'product_id', 'quantity']


class PackSerializer(serializers.ModelSerializer):
    pack_products = PackProductSerializer(source='packproduct_set', many=True, read_only=True)
    products = ProductSerializer(many=True, read_only=True)
    savings = serializers.ReadOnlyField()

    class Meta:
        model = Pack
        fields = '__all__'


class SiteSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SiteSettings
        fields = '__all__'


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = UserProfile
        fields = '__all__'


class CartItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    pack = PackSerializer(read_only=True)
    product_id = serializers.IntegerField(write_only=True, required=False)
    pack_id = serializers.IntegerField(write_only=True, required=False)
    item_name = serializers.ReadOnlyField()
    item_price = serializers.ReadOnlyField()
    total_price = serializers.ReadOnlyField()

    class Meta:
        model = CartItem
        fields = '__all__'

    def validate(self, data):
        product_id = data.get('product_id')
        pack_id = data.get('pack_id')

        if not product_id and not pack_id:
            raise serializers.ValidationError("Either product_id or pack_id must be provided")

        if product_id and pack_id:
            raise serializers.ValidationError("Cannot specify both product_id and pack_id")

        return data


class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total = serializers.SerializerMethodField()

    class Meta:
        model = Cart
        fields = '__all__'

    def get_total(self, obj):
        return sum(item.total_price for item in obj.items.all())


class OrderItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)

    class Meta:
        model = OrderItem
        fields = '__all__'


class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    order_items = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Order
        fields = '__all__'

    def create(self, validated_data):
        # Extract order_items from validated_data
        order_items_data = validated_data.pop('order_items', [])

        # Create the order
        order = Order.objects.create(**validated_data)

        # Create order items
        for item_data in order_items_data:
            try:
                product_id = item_data.get('product')
                quantity = int(item_data.get('quantity', 1))
                price = float(item_data.get('price', 0))

                # Get the product
                product = Product.objects.get(id=product_id)

                # Create the order item
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    quantity=quantity,
                    price=price
                )
            except (Product.DoesNotExist, ValueError, TypeError) as e:
                # Log the error but don't fail the entire order
                print(f"Error creating order item: {e}")
                continue

        return order





class SiteSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SiteSettings
        fields = '__all__'
