import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { productsAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';
import {
  FaShoppingCart,
  FaExclamationTriangle,
  FaArrowLeft,
  FaChevronRight,
  FaHome,
  FaCheck,
  FaTimes,
  FaBoxOpen
} from 'react-icons/fa';
import ProductCard from '../components/products/ProductCard';
import '../styles/product-detail.css';

const ProductDetail = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const { addToCart } = useCart();

  const [product, setProduct] = useState(null);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await productsAPI.getById(productId);
        setProduct(response.data);

        // Reset selected image and quantity when product changes
        setSelectedImage(0);
        setQuantity(1);

        // Fetch related products from the same category
        if (response.data.category) {
          const relatedResponse = await productsAPI.getByCategory(
            response.data.category,
            { exclude: productId, limit: 4 }
          );
          // Filter out the current product from related products
          const filteredRelated = (relatedResponse.data.results || []).filter(
            product => product.id.toString() !== productId.toString()
          );
          setRelatedProducts(filteredRelated);
        }
      } catch (err) {
        setError('Failed to load product details');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
    // Scroll to top when product changes
    window.scrollTo(0, 0);
  }, [productId]);

  // Handle quantity change
  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value);
    if (value > 0 && (!product || value <= product.stock)) {
      setQuantity(value);
    }
  };

  // Increment quantity
  const incrementQuantity = () => {
    if (product && quantity < product.stock) {
      setQuantity(quantity + 1);
    }
  };

  // Decrement quantity
  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  // Handle add to cart
  const handleAddToCart = async () => {
    if (product && product.id) {
      try {
        console.log('Product object:', product);
        console.log('Product ID:', product.id);
        console.log('Quantity:', quantity);

        // Validate product object
        if (!product.id || isNaN(parseInt(product.id, 10))) {
          console.error('Invalid product ID:', product.id);
          return;
        }

        // Validate quantity
        if (isNaN(parseInt(quantity, 10)) || parseInt(quantity, 10) <= 0) {
          console.error('Invalid quantity:', quantity);
          return;
        }

        await addToCart(product, quantity);
        console.log('Product added to cart successfully');
        // You could add a success message or toast notification here
      } catch (error) {
        console.error('Error adding product to cart:', error);
        // You could add an error message or toast notification here
      }
    } else {
      console.error('Product is undefined or missing ID');
    }
  };



  if (loading) {
    return (
      <div className="product-detail-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="product-detail-page">
        <div className="error-container">
          <FaExclamationTriangle size={50} color="#d32f2f" />
          <h2>Product Not Found</h2>
          <p>{error || 'The product you are looking for is not available or has been removed.'}</p>
          <button onClick={() => navigate('/products')} className="btn btn-primary">
            <FaArrowLeft /> Back to Products
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="product-detail-page">
      {/* Breadcrumb navigation */}
      <div className="breadcrumb">
        <Link to="/"><FaHome /> Home</Link>
        <FaChevronRight size={12} />
        <Link to="/products">Products</Link>
        <FaChevronRight size={12} />
        <Link to={`/products/category/${product.category}`}>{product.category_name}</Link>
        <FaChevronRight size={12} />
        <span>{product.name}</span>
      </div>

      <div className="product-detail-container">
        <div className="product-detail-left">
          {/* Product images */}
          <div className="product-images">
            <div className="main-image-container">
              {product.images && product.images.length > 0 ? (
                <img
                  src={product.images[selectedImage].image}
                  alt={product.name}
                  className="main-image"
                />
              ) : (
                <div className="main-image-placeholder">
                  No Image Available
                </div>
              )}
            </div>

            {product.images && product.images.length > 1 && (
              <div className="thumbnail-images">
                {product.images.map((image, index) => (
                  <div
                    key={index}
                    className={`thumbnail ${selectedImage === index ? 'active' : ''}`}
                    onClick={() => setSelectedImage(index)}
                  >
                    <img src={image.image} alt={`${product.name} - view ${index + 1}`} />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Product info */}
        <div className="product-info">
          <div className="product-header">
            <h1 className="product-name">{product.name}</h1>
            <div className="product-meta">
              <Link
                to={`/products/category/${product.category}`}
                className="product-category"
              >
                {product.category_name}
              </Link>
              <span className="product-availability">
                {product.is_available && product.stock > 0
                  ? <span className="in-stock"><FaCheck /> In Stock ({product.stock} items available)</span>
                  : <span className="out-of-stock"><FaTimes /> Out of Stock</span>}
              </span>
            </div>
            <div className="product-price-large">{product.price} DT</div>
          </div>

          {/* Product actions */}
          {product.is_available && product.stock > 0 && (
            <div className="product-actions-cool">
              <div className="product-actions-row">
                <div className="quantity-control-cool">
                  <button
                    className="quantity-btn minus"
                    onClick={decrementQuantity}
                    disabled={quantity <= 1}
                    aria-label="Decrease quantity"
                  >
                    -
                  </button>
                  <input
                    type="number"
                    value={quantity}
                    onChange={handleQuantityChange}
                    min="1"
                    max={product.stock}
                    aria-label="Quantity"
                  />
                  <button
                    className="quantity-btn plus"
                    onClick={incrementQuantity}
                    disabled={quantity >= product.stock}
                    aria-label="Increase quantity"
                  >
                    +
                  </button>
                </div>

                <button
                  className="cart-btn"
                  onClick={handleAddToCart}
                  disabled={!product.is_available || product.stock === 0}
                  title="Add to Cart"
                >
                  <FaShoppingCart />
                  <span>Add to Cart</span>
                </button>


              </div>
            </div>
          )}

          {(!product.is_available || product.stock <= 0) && (
            <div className="out-of-stock-message">
              <FaExclamationTriangle className="out-of-stock-icon" />
              <h3>Out of Stock</h3>
              <p>This product is currently unavailable. Please check back later.</p>
            </div>
          )}

          <div className="product-tabs">
            <div className="tab-content">
              <div className="product-description">
                <h2>Description</h2>
                <p>{product.description}</p>
              </div>

              <div className="product-details">
                <h2>Product Details</h2>
                <ul>
                  <li><strong>Cash on Delivery:</strong> Available throughout Tunisia</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Related products */}
      {relatedProducts.length > 0 && (
        <div className="related-products">
          <h2>Similar Products</h2>
          <div className="related-products-grid">
            {relatedProducts.map(relatedProduct => (
              <ProductCard
                key={relatedProduct.id}
                product={{
                  ...relatedProduct,
                  image: relatedProduct.images && relatedProduct.images.length > 0 ? relatedProduct.images[0].image : null,
                  category: {
                    id: relatedProduct.category,
                    name: relatedProduct.category_name || 'Supplements'
                  },
                  stock_quantity: relatedProduct.stock || 0,
                  is_featured: relatedProduct.is_featured || false
                }}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetail;
