import { Link } from 'react-router-dom';
import { useSiteSettings } from '../../contexts/SiteSettingsContext';

const Footer = () => {
  const { settings, getContactInfo, getSocialLinks } = useSiteSettings();
  const currentYear = new Date().getFullYear();
  const contactInfo = getContactInfo();
  const socialLinks = getSocialLinks();

  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-section">
          <h3>{settings?.site_name || 'Nordica Nutrition'}</h3>
          <p>{settings?.site_description || 'Your trusted source for premium nutrition supplements in Tunisia.'}</p>
          {contactInfo.address && <p>{contactInfo.address}</p>}
        </div>

        <div className="footer-section">
          <h3>Quick Links</h3>
          <ul className="footer-links">
            <li className="footer-link">
              <Link to="/">Home</Link>
            </li>
            <li className="footer-link">
              <Link to="/products">Products</Link>
            </li>
            <li className="footer-link">
              <Link to="/cart">Cart</Link>
            </li>
            <li className="footer-link">
              <Link to="/login">Login</Link>
            </li>
            <li className="footer-link">
              <Link to="/register">Register</Link>
            </li>
          </ul>
        </div>

        <div className="footer-section">
          <h3>Categories</h3>
          <ul className="footer-links">
            <li className="footer-link">
              <Link to="/products/category/1">Protein</Link>
            </li>
            <li className="footer-link">
              <Link to="/products/category/2">Pre-Workout</Link>
            </li>
            <li className="footer-link">
              <Link to="/products/category/3">Vitamins</Link>
            </li>
            <li className="footer-link">
              <Link to="/products/category/4">Weight Management</Link>
            </li>
            <li className="footer-link">
              <Link to="/products/category/5">Accessories</Link>
            </li>
          </ul>
        </div>

        <div className="footer-section">
          <h3>Contact Us</h3>
          {contactInfo.phone && (
            <p>
              <strong>Phone:</strong> {contactInfo.phone}
            </p>
          )}
          {contactInfo.email && (
            <p>
              <strong>Email:</strong> {contactInfo.email}
            </p>
          )}
          {contactInfo.businessHours && (
            <p>
              <strong>Hours:</strong> <br />
              <span style={{ whiteSpace: 'pre-line' }}>{contactInfo.businessHours}</span>
            </p>
          )}
          
          <h3 className="mt-3">Follow Us</h3>
          <div className="social-links">
            {socialLinks.facebook && (
              <a href={socialLinks.facebook} target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <i className="fab fa-facebook-f"></i>
              </a>
            )}
            {socialLinks.instagram && (
              <a href={socialLinks.instagram} target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <i className="fab fa-instagram"></i>
              </a>
            )}
            {socialLinks.tiktok && (
              <a href={socialLinks.tiktok} target="_blank" rel="noopener noreferrer" aria-label="TikTok">
                <i className="fab fa-tiktok"></i>
              </a>
            )}
          </div>
        </div>
      </div>

      <div className="copyright">
        <p>&copy; {currentYear} Nordica Nutrition. All rights reserved.</p>
        <p>Cash on delivery available throughout Tunisia.</p>
      </div>
    </footer>
  );
};

export default Footer;
