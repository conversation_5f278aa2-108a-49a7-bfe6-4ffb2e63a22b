/* NORDICA NUTRITION - PRODUCT PAGE */

/* Main Container */
.product-list-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
  background: var(--gray-light);
  min-height: 100vh;
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  padding: var(--space-3) var(--space-4);
  background: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow);
  font-size: 0.9rem;
  color: var(--gray);
}

.breadcrumb a {
  color: var(--gray);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  transition: all 0.2s ease;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
}

.breadcrumb a:hover {
  color: var(--red);
  background: rgba(220, 38, 38, 0.1);
}

.breadcrumb span {
  color: var(--black);
  font-weight: 600;
}

/* Page Header */
.product-list-header {
  margin-bottom: var(--space-8);
  text-align: center;
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.product-list-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--space-3);
  position: relative;
  display: inline-block;
  padding-bottom: var(--space-3);
}

.product-list-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.product-list-header p,
.product-list-header .category-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Mobile Filter Toggle */
.filter-toggle-btn {
  display: none;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--white);
  color: var(--red);
  border: 2px solid var(--red);
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--space-4);
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  box-shadow: var(--shadow);
}

.filter-toggle-btn:hover {
  background: var(--red);
  color: var(--white);
  transform: translateY(-1px);
}

.filter-toggle-btn svg {
  font-size: 1.1rem;
}

/* Main Layout */
.product-list-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-6);
  align-items: start;
}

/* Filter Sidebar */
.product-filters {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: var(--space-4);
  height: fit-content;
  max-height: calc(100vh - var(--space-8));
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border);
}

.filter-header h2 {
  font-size: 1.3rem;
  color: var(--black);
  margin: 0;
  font-weight: 600;
}

.filter-close-btn {
  display: none;
  background: none;
  border: none;
  color: var(--gray);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.filter-close-btn:hover {
  color: var(--red);
  background: rgba(220, 38, 38, 0.1);
}

/* Clear Filters Button - Simple & Beautiful */
.clear-filters-btn {
  padding: var(--space-2) var(--space-3);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--space-4);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.clear-filters-btn:disabled {
  background: var(--gray);
  cursor: not-allowed;
}

.clear-filters-btn:not(:disabled):hover {
  background: var(--red-dark);
  transform: translateY(-1px);
}

.clear-filters-btn svg {
  font-size: 0.75rem;
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.8rem;
  margin-left: var(--space-1);
}

/* Filter Sections */
.filter-section {
  margin-bottom: var(--space-4);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.filter-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: var(--gray-light);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-section-header:hover {
  background: rgba(220, 38, 38, 0.1);
}

.filter-section-header h3 {
  font-size: 1rem;
  color: var(--black);
  margin: 0;
  font-weight: 600;
}

.filter-section-header svg {
  color: var(--gray);
  transition: transform 0.2s ease;
}

.filter-section-content {
  padding: var(--space-4);
  background: var(--white);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filter-section-content.expanded {
  max-height: 500px;
}

/* Category List */
.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  margin-bottom: var(--space-2);
}

.category-list button {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  background: none;
  border: 1px solid var(--border);
  border-radius: 6px;
  color: var(--gray);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-align: left;
}

.category-list button:hover {
  background: var(--gray-light);
  border-color: var(--red);
  color: var(--red);
}

.category-list button.active {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
}

/* Price Filter */
.price-filter {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* Simple Price Range Slider */
.price-range-slider {
  margin-bottom: var(--space-3);
}

.range-values {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--red);
}

.range-container {
  position: relative;
  height: 20px;
}

.range-slider {
  position: absolute;
  width: 100%;
  height: 6px;
  background: transparent;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  pointer-events: none;
}

.range-slider::-webkit-slider-track {
  width: 100%;
  height: 6px;
  background: var(--border);
  border-radius: 3px;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--red);
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all;
  border: 2px solid var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.range-slider::-moz-range-track {
  width: 100%;
  height: 6px;
  background: var(--border);
  border-radius: 3px;
  border: none;
}

.range-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--red);
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-min {
  z-index: 1;
}

.range-max {
  z-index: 2;
}

.price-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.price-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.price-input-group label {
  font-size: 0.9rem;
  color: var(--gray);
  font-weight: 500;
}

.price-input-group input {
  padding: var(--space-2);
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.price-input-group input:focus {
  outline: none;
  border-color: var(--red);
}

.price-currency {
  font-size: 0.8rem;
  color: var(--gray);
  margin-top: var(--space-1);
}

.apply-price-btn {
  padding: var(--space-2) var(--space-3);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.apply-price-btn:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
}

/* Availability Filter */
.availability-filter {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.switch-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: var(--black);
  font-weight: 500;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border);
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--red);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Apply Filters Button */
.apply-filters-btn {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: var(--space-4);
}

.apply-filters-btn:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
}

/* Product Grid Container */
.product-grid-container {
  background: var(--white);
  border-radius: 12px;
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
}

/* Product Controls */
.product-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border);
}

.product-count {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.product-count svg {
  color: var(--red);
  font-size: 1.1rem;
}

.product-sort {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.product-sort label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.product-sort svg {
  color: var(--red);
  font-size: 1rem;
}

.sort-dropdown {
  position: relative;
}

.sort-dropdown select {
  padding: var(--space-2) var(--space-3);
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--black);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 180px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.sort-dropdown select:hover {
  border-color: var(--red);
}

.sort-dropdown select:focus {
  outline: none;
  border-color: var(--red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Product Grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-4);
}

/* Product Cards */
.product-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--red);
}

/* Card Image */
.card-img-container {
  position: relative;
  overflow: hidden;
  display: block;
  text-decoration: none;
  height: 200px;
  background: var(--gray-light);
}

.card-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.card-img-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-light);
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.product-card:hover .card-img {
  transform: scale(1.05);
}

/* Card Body */
.card-body {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  flex: 1;
}

.card-title-link {
  text-decoration: none;
  margin-bottom: var(--space-2);
}

.card-title {
  font-size: 1.1rem;
  color: var(--black);
  margin: 0;
  font-weight: 600;
  line-height: 1.3;
  transition: color 0.2s ease;
}

.card-title-link:hover .card-title {
  color: var(--red);
}

.card-text {
  margin: 0 0 var(--space-3) 0;
  font-size: 0.9rem;
}

.card-text a {
  color: var(--gray);
  text-decoration: none;
  transition: color 0.2s ease;
}

.card-text a:hover {
  color: var(--red);
}

/* Card Meta - Enhanced */
.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-3) var(--space-4);
  background: linear-gradient(135deg, #fafafa, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.card-meta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--red), #f59e0b, var(--red));
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.card-price {
  font-size: 1.6rem;
  font-weight: 900;
  color: var(--red);
  margin: 0;
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
  position: relative;
}

.card-price::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--red), transparent);
  border-radius: 1px;
}

.card-price::after {
  content: 'DT';
  font-size: 1rem;
  font-weight: 700;
  color: var(--gray);
  background: var(--white);
  padding: var(--space-1) var(--space-2);
  border-radius: 6px;
  border: 1px solid var(--border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-status {
  padding: var(--space-2) var(--space-4);
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  box-shadow: 0 0 8px currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

.card-status::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.card-status:hover::after {
  left: 100%;
}

.card-status.in-stock {
  background: linear-gradient(135deg, #10b981, #059669);
  color: var(--white);
  border: 1px solid #047857;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.card-status.in-stock::before {
  background: #34d399;
  box-shadow: 0 0 12px #34d399;
}

.card-status.out-of-stock {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: var(--white);
  border: 1px solid #b91c1c;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.card-status.out-of-stock::before {
  background: #f87171;
  box-shadow: 0 0 12px #f87171;
}

.card-status.out-of-stock {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Card Actions */
.card-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: auto;
}

.card-actions .btn {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  text-decoration: none;
  border: none;
}

.btn-outline {
  background: var(--white);
  color: var(--gray);
  border: 1px solid var(--border);
}

.btn-outline:hover {
  background: var(--gray-light);
  border-color: var(--red);
  color: var(--red);
}

.btn-primary {
  background: var(--red);
  color: var(--white);
  border: 1px solid var(--red);
}

.btn-primary:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: var(--gray);
  border-color: var(--gray);
  cursor: not-allowed;
  transform: none;
}

.btn svg {
  font-size: 0.9rem;
}

/* No Products Message */
.no-products {
  text-align: center;
  padding: var(--space-8) var(--space-4);
  color: var(--gray);
}

.no-products-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.no-products h3 {
  font-size: 1.5rem;
  margin: 0 0 var(--space-2) 0;
  color: var(--black);
}

.no-products p {
  font-size: 1rem;
  margin: 0 0 var(--space-4) 0;
}

.clear-all-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.clear-all-btn:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
}

/* Loading States */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  color: var(--gray);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .product-list-container {
    grid-template-columns: 250px 1fr;
    gap: var(--space-4);
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .product-list-page {
    padding: var(--space-3);
  }

  .filter-toggle-btn {
    display: flex;
  }

  .product-list-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .product-filters {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
    border-radius: 0;
    max-height: none;
    overflow-y: auto;
  }

  .product-filters.open {
    left: 0;
  }

  .product-filters.open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }

  .filter-close-btn {
    display: block;
  }

  .product-list-header h1 {
    font-size: 2rem;
  }

  .product-controls {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-3);
  }

  .card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .product-list-page {
    padding: var(--space-2);
  }

  .product-filters {
    width: 100%;
    left: -100%;
  }

  .product-filters.open::before {
    left: 100%;
  }

  .breadcrumb {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }

  .product-list-header {
    padding: var(--space-6) var(--space-3);
  }

  .product-list-header h1 {
    font-size: 1.8rem;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .price-inputs {
    grid-template-columns: 1fr;
  }
}

/* PAGINATION STYLES */
.pagination-info {
  text-align: center;
  margin: var(--space-4) 0 var(--space-2) 0;
}

.pagination-info p {
  color: var(--gray);
  font-size: 0.9rem;
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-2);
  margin: var(--space-4) 0 var(--space-6) 0;
  padding: var(--space-4) 0;
}

.pagination-button {
  padding: var(--space-3) var(--space-4);
  background: var(--white);
  border: 2px solid var(--border);
  color: var(--gray);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  min-width: 100px;
  justify-content: center;
}

.pagination-button:hover:not(:disabled) {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.pagination-button:disabled {
  background: #f8f9fa;
  color: #adb5bd;
  border-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.6;
}

.pagination-pages {
  display: flex;
  gap: var(--space-1);
  margin: 0 var(--space-3);
}

.pagination-page {
  width: 44px;
  height: 44px;
  padding: 0;
  background: var(--white);
  border: 2px solid var(--border);
  color: var(--gray);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover:not(.active) {
  background: #f8f9fa;
  border-color: var(--red);
  color: var(--red);
  transform: translateY(-1px);
}

.pagination-page.active {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  transform: translateY(-1px);
}

.pagination-page.active:hover {
  background: var(--red-dark);
  border-color: var(--red-dark);
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: var(--gray);
  font-weight: 600;
  font-size: 0.9rem;
  user-select: none;
}

/* Pagination responsive styles */
@media (max-width: 768px) {
  .pagination {
    gap: var(--space-1);
    margin: var(--space-6) 0 var(--space-4) 0;
  }

  .pagination-button {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
    min-width: 80px;
  }

  .pagination-page {
    width: 36px;
    height: 36px;
    font-size: 0.8rem;
  }

  .pagination-pages {
    margin: 0 var(--space-2);
  }
}

@media (max-width: 480px) {
  .pagination {
    flex-wrap: wrap;
    gap: var(--space-1);
  }

  .pagination-button {
    padding: var(--space-2);
    font-size: 0.75rem;
    min-width: 70px;
  }

  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}
