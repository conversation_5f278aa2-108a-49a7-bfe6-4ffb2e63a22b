/* Home Page Styles - Simple & Clean */
.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
}

/* Hero Section - Simple & Clean */
.hero {
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  padding: var(--space-12) 0;
  text-align: center;
  border-radius: 8px;
  margin-bottom: var(--space-8);
}

.hero-content {
  max-width: 800px;
  z-index: 1;
}

.hero-logo {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
  width: 100%;
}

.hero-logo-img {
  height: 200px;
  width: auto;
  object-fit: contain;
  margin-bottom: 2rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive logo styles */
@media (max-width: 768px) {
  .hero-logo-img {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .hero-logo-img {
    height: 120px;
  }
}

.hero h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero .btn-outline {
  padding: var(--space-3) var(--space-6);
  font-size: 1rem;
  font-weight: 600;
  background-color: transparent;
  color: var(--white);
  border: 2px solid var(--white);
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.hero .btn-outline:hover {
  background-color: var(--white);
  color: var(--red);
  border-color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-bottom: 0.5rem;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #FF0000, #ff3333);
  border-radius: 3px;
}

.view-all {
  color: #FF0000;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.view-all:hover {
  color: #CC0000;
  transform: translateX(5px);
}

/* Grid Layouts */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-4 {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* Featured Packs Section */
.featured-packs {
  margin-bottom: 3rem;
  padding: 0 var(--space-4);
}

.featured-packs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-6);
}

/* Grid styling is now handled in simple-product-card.css */

.product-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
  box-shadow: var(--shadow);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.product-card.featured {
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.featured-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #FF0000, #ff3333);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 2px 5px rgba(255, 0, 0, 0.3);
  z-index: 10;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-img-container {
  position: relative;
  overflow: hidden;
  display: block;
}

.card-img {
  width: 100%;
  height: 200px;
  object-fit: contain;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
  transition: transform 0.5s ease;
}

.product-card:hover .card-img {
  transform: scale(1.05);
}

.card-img-placeholder {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  color: #aaa;
  font-size: 0.9rem;
  border-bottom: 1px solid #eee;
}

.card-body {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.card-title-link {
  text-decoration: none;
  color: inherit;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  transition: color 0.3s ease;
}

.card-title:hover {
  color: #FF0000;
}

.card-text {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.card-text a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s ease;
}

.card-text a:hover {
  color: #FF0000;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0 1rem 0;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.card-price {
  font-size: 1.3rem;
  font-weight: 600;
  color: #FF0000;
  margin: 0;
}

.card-status {
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.card-status.in-stock {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.card-status.out-of-stock {
  background-color: #ffebee;
  color: #FF0000;
}

.card-actions {
  margin-top: auto;
  display: flex;
  justify-content: center;
}

.product-card .btn {
  width: 100%;
  text-align: center;
  padding: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

/* Categories Section */
.categories-section {
  margin-bottom: 3rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.category-card {
  position: relative;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card-1 {
  grid-column: span 2;
}

.category-card-2, .category-card-3 {
  grid-column: span 1;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.category-card a {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.category-card:hover .category-img {
  transform: scale(1.05);
}

.category-img-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e53935, #ff6b6b);
  color: white;
  font-size: 3rem;
  font-weight: 600;
}

.category-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  text-align: left;
}

.category-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.category-description {
  font-size: 1rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  max-width: 80%;
}

.category-content .btn {
  background-color: white;
  color: #333;
  border: none;
  padding: 0.6rem 1.2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.category-content .btn:hover {
  background-color: #FF0000;
  color: white;
  transform: translateY(-2px);
}

/* About Section */
.about-section {
  margin-bottom: 3rem;
}

.about-content {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.about-text {
  flex: 1;
  line-height: 1.6;
  color: #555;
}

.about-text p {
  margin-bottom: 1rem;
}

.about-text p:last-child {
  margin-bottom: 0;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero {
    height: 80vh;
    min-height: 500px;
  }

  .hero h1 {
    font-size: 3rem;
  }

  .hero-logo-img {
    height: 180px;
  }

  .about-content {
    flex-direction: column;
  }

  .featured-packs-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .category-card-1,
  .category-card-2,
  .category-card-3 {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .hero {
    height: 70vh;
    min-height: 450px;
    padding: 1.5rem;
  }

  .hero-content {
    padding: 1.5rem;
  }

  .hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .hero p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .hero-logo-img {
    height: 150px;
    margin-bottom: 1.5rem;
  }

  .hero .btn {
    padding: 0.7rem 1.8rem;
    font-size: 1rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .category-content h3 {
    font-size: 1.5rem;
  }

  .category-description {
    font-size: 0.9rem;
    max-width: 100%;
  }

  .featured-badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }

  .card-meta {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 576px) {
  .hero {
    height: 60vh;
    min-height: 400px;
    padding: 1rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .hero h1 {
    font-size: 2rem;
    margin-bottom: 0.75rem;
  }

  .hero p {
    font-size: 1rem;
    margin-bottom: 1.25rem;
  }

  .hero-logo {
    margin-bottom: 1.2rem;
  }

  .hero-logo-img {
    height: 120px;
    margin-bottom: 1.2rem;
  }

  .hero .btn {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
    width: auto;
    min-width: 140px;
  }

  .category-card {
    height: 250px;
  }

  .category-content {
    padding: 1.5rem;
  }

  .card-body {
    padding: 1rem;
  }

  .card-title {
    font-size: 1.1rem;
  }

  .card-price {
    font-size: 1.1rem;
  }

  .product-card .btn {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 360px) {
  .hero {
    height: 50vh;
    min-height: 350px;
  }

  .hero h1 {
    font-size: 1.75rem;
  }

  .hero p {
    font-size: 0.9rem;
  }

  .hero-logo-img {
    height: 100px;
  }
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  font-size: 3rem;
  color: #FF0000;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  color: #FF0000;
  margin-bottom: 1rem;
}

.error-container h2 {
  color: #FF0000;
  margin-bottom: 1rem;
}

.error-container p {
  color: #666;
  margin-bottom: 1.5rem;
  max-width: 500px;
}

/* Empty Section */
.empty-section {
  text-align: center;
  padding: 3rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.empty-section p {
  color: #666;
  margin-bottom: 1.5rem;
}

.empty-section .btn {
  display: inline-block;
}

/* CATEGORY SLIDER STYLES */
.category-slider-section {
  margin-bottom: var(--space-8);
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.category-slider-container {
  position: relative;
  width: 100%;
  height: 400px;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
}

.slider-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--white);
  gap: var(--space-3);
}

.slider-loading .loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.category-slider {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.category-slides {
  display: flex;
  width: 100%;
  height: 100%;
}

.category-slide {
  min-width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.category-slide-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1000px;
  width: 100%;
  padding: 0 var(--space-8);
  gap: var(--space-8);
}

.category-slide-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 400px;
}

.category-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.category-image:hover {
  transform: scale(1.05);
}

.category-image-placeholder {
  width: 100%;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.category-slide-info {
  flex: 1;
  color: var(--white);
  text-align: left;
  max-width: 500px;
}

.category-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.category-description {
  font-size: 1.2rem;
  margin-bottom: var(--space-6);
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.category-shop-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: var(--white);
  color: var(--red);
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.category-shop-btn:hover {
  background: var(--gray-light);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  color: var(--red-dark);
}

/* Navigation Arrows */
.slider-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--white);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.slider-nav:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.slider-nav.prev {
  left: var(--space-4);
}

.slider-nav.next {
  right: var(--space-4);
}

/* Dots Indicator */
.slider-dots {
  position: absolute;
  bottom: var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-2);
  z-index: 10;
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot.active {
  background: var(--white);
  border-color: var(--white);
}

.slider-dot:hover {
  border-color: var(--white);
  transform: scale(1.2);
}

.no-categories {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.7);
  gap: var(--space-3);
  font-size: 1.1rem;
}

/* RESPONSIVE CATEGORY SLIDER */
@media (max-width: 768px) {
  .category-slider-container {
    height: 500px;
  }

  .category-slide-content {
    flex-direction: column;
    text-align: center;
    padding: var(--space-4);
    gap: var(--space-4);
  }

  .category-slide-image {
    max-width: 280px;
  }

  .category-image {
    height: 200px;
  }

  .category-image-placeholder {
    height: 200px;
  }

  .category-title {
    font-size: 2rem;
    margin-bottom: var(--space-3);
  }

  .category-description {
    font-size: 1rem;
    margin-bottom: var(--space-4);
  }

  .category-shop-btn {
    padding: var(--space-3) var(--space-5);
    font-size: 1rem;
  }

  .slider-nav {
    width: 40px;
    height: 40px;
  }

  .slider-nav.prev {
    left: var(--space-2);
  }

  .slider-nav.next {
    right: var(--space-2);
  }
}

@media (max-width: 480px) {
  .category-slider-container {
    height: 450px;
  }

  .category-slide-content {
    padding: var(--space-3);
  }

  .category-slide-image {
    max-width: 240px;
  }

  .category-image {
    height: 160px;
  }

  .category-image-placeholder {
    height: 160px;
  }

  .category-title {
    font-size: 1.5rem;
  }

  .category-description {
    font-size: 0.9rem;
  }

  .category-shop-btn {
    padding: var(--space-2) var(--space-4);
    font-size: 0.9rem;
  }
}
