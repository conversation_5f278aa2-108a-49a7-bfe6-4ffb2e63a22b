import { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate, Link } from 'react-router-dom';
import { productsAPI, categoriesAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { FaBoxOpen, FaSort, FaUndo, FaLock, FaSearch, FaExclamationTriangle, FaEye, FaShoppingCart, FaHome, FaChevronRight, FaFilter } from 'react-icons/fa';
import ProductCard from '../components/products/ProductCard';
import EnhancedFilter from '../components/products/EnhancedFilter';
import SortDropdown from '../components/products/SortDropdown';
import '../styles/product-list.css';

const ProductList = () => {
  const { categoryId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { addToCart } = useCart();

  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Filtering and sorting state
  const [filters, setFilters] = useState({
    minPrice: '',
    maxPrice: '',
    inStock: false,
  });
  const [sortOption, setSortOption] = useState('newest');

  // Pagination state
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Mobile filter state
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Get query params
  const queryParams = new URLSearchParams(location.search);
  const searchQuery = queryParams.get('search') || '';
  const categoryQuery = queryParams.get('category') || '';

  // Toggle mobile filters
  const toggleMobileFilters = () => {
    setIsMobileFilterOpen(!isMobileFilterOpen);
  };

  // Close mobile filters
  const closeMobileFilters = () => {
    setIsMobileFilterOpen(false);
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await categoriesAPI.getAll();
        setCategories(response.data.results || []);

        const effectiveCategoryId = categoryId || categoryQuery;
        if (effectiveCategoryId) {
          const category = response.data.results.find(cat => cat.id.toString() === effectiveCategoryId);
          setCurrentCategory(category || null);

          // Auto-select the category in the filter sidebar
          if (category) {
            console.log('🎯 Auto-selecting category in sidebar:', category.name);
            setFilters(prev => ({
              ...prev,
              category: category.id.toString()
            }));
          }
        } else {
          setCurrentCategory(null);
          // Clear category filter if no category specified
          setFilters(prev => ({
            ...prev,
            category: ''
          }));
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };

    fetchCategories();
  }, [categoryId || '', categoryQuery || '']);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        // Prepare query parameters
        const params = {
          page,
          search: searchQuery,
          ordering: getSortingParam(sortOption),
        };

        // Add category filter if specified (from route or query param)
        const effectiveCategoryId = categoryId || categoryQuery;
        if (effectiveCategoryId) {
          params.category = effectiveCategoryId;
          console.log('🏷️ Filtering by category:', effectiveCategoryId);
        }

        // Add price filters if specified
        if (filters.minPrice) {
          params.min_price = filters.minPrice;
        }

        if (filters.maxPrice) {
          params.max_price = filters.maxPrice;
        }

        // Add stock filter if specified
        if (filters.inStock) {
          params.is_available = true;
        }

        // Fetch products with filters
        const response = effectiveCategoryId
          ? await productsAPI.getByCategory(effectiveCategoryId, params)
          : await productsAPI.getAll(params);

        setProducts(response.data.results || []);

        // Calculate total pages with improved logic
        const count = response.data.count || 0;
        const pageSize = response.data.page_size || 20; // Match Django's PAGE_SIZE
        const actualProducts = response.data.results?.length || 0;
        const hasNext = !!response.data.next;
        const hasPrevious = !!response.data.previous;

        let calculatedPages;

        // If we have accurate count and pageSize from Django
        if (count > 0 && pageSize > 0) {
          calculatedPages = Math.ceil(count / pageSize);
        } else if (actualProducts === 0 && page === 1) {
          // No products at all
          calculatedPages = 1;
        } else if (actualProducts === 0 && page > 1) {
          // We're on an empty page beyond the actual data
          // This means the previous page was likely the last page
          calculatedPages = Math.max(1, page - 1);
          console.warn('⚠️ Empty page detected, adjusting total pages to:', calculatedPages);

          // Auto-redirect to the last valid page
          setTimeout(() => {
            console.log('🔄 Redirecting to last valid page:', calculatedPages);
            setPage(calculatedPages);
          }, 100);
        } else if (actualProducts > 0 && !hasNext) {
          // Current page has products and no next page = this is the last page
          calculatedPages = page;
        } else if (actualProducts > 0 && hasNext) {
          // Current page has products and there's a next page
          // We can't know exactly how many more pages, so be conservative
          calculatedPages = page + 1;
        } else {
          // Fallback
          calculatedPages = Math.max(1, page);
        }

        console.log('📊 Pagination Debug:', {
          totalProducts: count,
          pageSize: pageSize,
          currentPage: page,
          actualProductsReceived: actualProducts,
          hasNext: hasNext,
          hasPrevious: hasPrevious,
          calculatedPages: calculatedPages,
          willRedirect: actualProducts === 0 && page > 1
        });

        // Safety check and set total pages
        const safePages = Math.max(1, Math.min(calculatedPages, 20)); // Reasonable max
        setTotalPages(safePages);

      } catch (err) {
        console.error('Error fetching products:', err);

        // If we're on a page beyond the actual data, redirect to page 1
        if (page > 1 && (err.response?.status === 404 || err.message?.includes('404'))) {
          console.warn('⚠️ Page not found, redirecting to page 1');
          setPage(1);
          setError(null); // Clear error since we're redirecting
        } else {
          setError('Failed to load products');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId || '', categoryQuery || '', page, searchQuery || '', sortOption || '', filters]);

  // Auto-redirect if on invalid page
  useEffect(() => {
    if (totalPages > 0 && page > totalPages) {
      console.warn('⚠️ Current page', page, 'exceeds total pages', totalPages, '- redirecting to page 1');
      setPage(1);
    }
  }, [totalPages, page]);

  // Helper function to get sorting parameter
  const getSortingParam = (option) => {
    switch (option) {
      case 'priceAsc':
        return 'price';
      case 'priceDesc':
        return '-price';
      case 'nameAsc':
        return 'name';
      case 'nameDesc':
        return '-name';
      case 'newest':
      default:
        return '-created_at';
    }
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setPage(1); // Reset to first page when filters change
  };

  // Handle sort change
  const handleSortChange = (e) => {
    setSortOption(e.target.value);
    setPage(1); // Reset to first page when sort changes
  };

  // Clear all filters
  const handleClearFilters = () => {
    // Reset all filter states
    setFilters({
      minPrice: '',
      maxPrice: '',
      inStock: false,
    });
    setSortOption('newest');
    setPage(1);

    // If we're on a category page or have search query, navigate to main products page
    if (categoryId || searchQuery) {
      navigate('/products');
    }
  };

  // Handle add to cart
  const handleAddToCart = async (product) => {
    // Validate product
    if (!product || !product.id) {
      console.error('Invalid product object:', product);
      return;
    }

    try {
      console.log('Adding product to cart:', product);
      await addToCart(product, 1);
      console.log('Product added to cart successfully');
    } catch (error) {
      console.error('Error adding product to cart:', error);
    }
  };



  // Handle page change with validation
  const handlePageChange = (newPage) => {
    // Ensure the new page is within valid range
    if (newPage < 1 || newPage > totalPages) {
      console.warn('⚠️ Attempted to navigate to invalid page:', newPage, 'Valid range: 1 -', totalPages);
      return;
    }

    console.log('📄 Navigating to page:', newPage);
    setPage(newPage);
    window.scrollTo(0, 0);
  };

  // Only show full page loader on initial load
  if (loading && page === 1 && products.length === 0) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading products...</p>
      </div>
    );
  }

  return (
    <div className="product-list-page">
      {/* Breadcrumb navigation */}
      <div className="breadcrumb">
        <Link to="/"><FaHome /> Home</Link>
        <FaChevronRight size={12} />
        <Link to="/products">Products</Link>
        {currentCategory && (
          <>
            <FaChevronRight size={12} />
            <span>{currentCategory.name}</span>
          </>
        )}
        {searchQuery && (
          <>
            <FaChevronRight size={12} />
            <span>Search: "{searchQuery}"</span>
          </>
        )}
      </div>

      <div className="product-list-header">
        <h1>
          {currentCategory
            ? `${currentCategory.name} Products`
            : searchQuery
              ? `Search Results for "${searchQuery}"`
              : 'All Products'}
        </h1>

        {currentCategory && currentCategory.description && (
          <p className="category-description">{currentCategory.description}</p>
        )}

        {!currentCategory && !searchQuery && (
          <p>Discover our complete range of premium nutrition supplements</p>
        )}
      </div>

      {/* Mobile Filter Toggle Button */}
      <button
        className="filter-toggle-btn"
        onClick={toggleMobileFilters}
      >
        <FaFilter /> Filters & Sort
      </button>

      <div className="product-list-container">
        {/* Enhanced sidebar with filters */}
        <EnhancedFilter
          categories={categories}
          currentCategoryId={categoryId}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          maxPriceLimit={500}
          isMobileFilterOpen={isMobileFilterOpen}
          onCloseMobileFilter={closeMobileFilters}
        />

        {/* Main product grid */}
        <div className="product-grid-container">
          {/* Loading overlay */}
          {loading && page !== 1 && (
            <div className="product-grid-loading fade-in">
              <div className="loading-spinner"></div>
              <p>Updating products...</p>
            </div>
          )}

          {/* Sorting and results count */}
          <div className="product-controls">
            <div className="product-count">
              <FaBoxOpen /> {products.length} products found
            </div>

            <div className="product-sort">
              <label>
                <FaSort /> Sort by:
              </label>
              <SortDropdown
                value={sortOption}
                onChange={handleSortChange}
              />
            </div>
          </div>

          {error && (
            <div className="error-message">
              <FaExclamationTriangle size={40} />
              <h3>Error Loading Products</h3>
              <p>{error}</p>
              {page > 1 ? (
                <button onClick={() => setPage(1)} className="btn btn-primary">
                  Go to First Page
                </button>
              ) : (
                <button onClick={() => window.location.reload()} className="btn btn-primary">
                  Try Again
                </button>
              )}
            </div>
          )}

          {!error && products.length === 0 ? (
            <div className="no-products">
              <div className="no-products-icon">
                <FaSearch />
              </div>
              <h3>No products found</h3>
              <p>We couldn't find any products matching your criteria.</p>
              <button
                onClick={handleClearFilters}
                className="btn btn-primary clear-all-btn"
                title="Clear all filters and show all products"
              >
                <FaUndo /> Clear All Filters & Show All Products
              </button>
            </div>
          ) : (
            <>
              <div className="products-grid">
                {products.map(product => (
                  <ProductCard
                    key={product.id}
                    product={{
                      ...product,
                      image: product.images && product.images.length > 0 ? product.images[0].image : null,
                      category: {
                        id: product.category,
                        name: product.category_name || 'Uncategorized'
                      },
                      stock_quantity: product.stock,
                      is_featured: product.is_featured || false
                    }}
                  />
                ))}
              </div>

              {/* Pagination Info */}
              {!error && totalPages > 1 && products.length > 0 && (
                <div className="pagination-info">
                  <p>Page {page} of {totalPages}</p>
                </div>
              )}

              {/* Pagination */}
              {!error && totalPages > 1 && products.length > 0 && (
                <div className="pagination">
                  <button
                    onClick={() => handlePageChange(page - 1)}
                    disabled={page === 1 || loading}
                    className="pagination-button"
                  >
                    Previous
                  </button>

                  <div className="pagination-pages">
                    {(() => {
                      const pages = [];
                      const maxVisiblePages = 3; // Reduced to be more conservative

                      // Be very conservative with page display
                      const safeTotalPages = Math.min(totalPages, 10); // Never show more than 10 pages

                      if (safeTotalPages <= maxVisiblePages) {
                        // Show all pages if total is small
                        for (let i = 1; i <= safeTotalPages; i++) {
                          pages.push(i);
                        }
                      } else {
                        // Show current page and immediate neighbors only
                        const startPage = Math.max(1, page - 1);
                        const endPage = Math.min(safeTotalPages, page + 1);

                        // Show first page if not in range
                        if (startPage > 1) {
                          pages.push(1);
                          if (startPage > 2) {
                            pages.push('...');
                          }
                        }

                        // Show pages around current page
                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(i);
                        }

                        // Show last page if not in range
                        if (endPage < safeTotalPages) {
                          if (endPage < safeTotalPages - 1) {
                            pages.push('...');
                          }
                          pages.push(safeTotalPages);
                        }
                      }

                      return pages.map((p, index) => {
                        if (p === '...') {
                          return (
                            <span key={`ellipsis-${index}`} className="pagination-ellipsis">
                              ...
                            </span>
                          );
                        }

                        return (
                          <button
                            key={p}
                            onClick={() => handlePageChange(p)}
                            disabled={loading}
                            className={`pagination-page ${p === page ? 'active' : ''}`}
                          >
                            {p}
                          </button>
                        );
                      });
                    })()}
                  </div>

                  <button
                    onClick={() => handlePageChange(page + 1)}
                    disabled={page === totalPages || loading}
                    className="pagination-button"
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductList;
