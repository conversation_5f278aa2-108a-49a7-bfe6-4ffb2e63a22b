/* SIMPLE CLEAN CSS - RED, WH<PERSON><PERSON>, B<PERSON><PERSON><PERSON> THEME */
:root {
  /* Colors - Simple & Clean */
  --red: #dc2626;
  --red-dark: #b91c1c;
  --red-light: #ef4444;
  --black: #1f2937;
  --gray: #6b7280;
  --gray-light: #f3f4f6;
  --white: #ffffff;
  --border: #e5e7eb;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;

  /* Shadows */
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* RESET & BASE */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  background: var(--white);
  color: var(--black);
  line-height: 1.6;
}

/* TYPOGRAPHY */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-4);
}

h1 { font-size: 2.5rem; color: var(--red); }
h2 { font-size: 2rem; color: var(--black); }
h3 { font-size: 1.5rem; color: var(--black); }
h4 { font-size: 1.25rem; color: var(--black); }

p {
  margin-bottom: var(--space-4);
  color: var(--gray);
}

a {
  color: var(--red);
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: var(--red-dark);
}

/* LAYOUT */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* HEADER - Enhanced Navigation */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header.scrolled {
  box-shadow: var(--shadow-lg);
  border-bottom-color: rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-8) var(--space-4);
}

.logo {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  transition: transform 0.2s;
}

.logo-link:hover {
  transform: scale(1.05);
}

.logo-img {
  height: 80px;
  width: auto;
  object-fit: contain;
}

.nav {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  color: var(--gray);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s;
  font-weight: 600;
  font-size: 1rem;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--red);
  background: var(--gray-light);
  transform: translateY(-1px);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--red);
  border-radius: 1px;
}

.nav-link svg {
  font-size: 1.1rem;
}

/* Cart Badge */
.icon-with-badge {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--red);
  color: var(--white);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Mobile Actions */
.mobile-actions {
  display: none;
  align-items: center;
  gap: var(--space-3);
}

.mobile-cart {
  color: var(--gray);
  font-size: 1.4rem;
  transition: all 0.2s;
  padding: var(--space-3);
  border-radius: 8px;
}

.mobile-cart:hover {
  color: var(--red);
  background: var(--gray-light);
}

.mobile-menu-btn {
  background: none;
  border: none;
  color: var(--gray);
  font-size: 1.4rem;
  cursor: pointer;
  padding: var(--space-3);
  border-radius: 8px;
  transition: all 0.2s;
}

.mobile-menu-btn:hover {
  color: var(--red);
  background: var(--gray-light);
}

/* BUTTONS */
.btn {
  display: inline-flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  font-size: 1rem;
}

.btn:hover {
  background: var(--red-dark);
  transform: translateY(-1px);
  color: var(--white);
}

.btn-outline {
  background: transparent;
  color: var(--red);
  border: 1px solid var(--red);
}

.btn-outline:hover {
  background: var(--red);
  color: var(--white);
}

.btn-secondary {
  background: var(--gray-light);
  color: var(--black);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background: var(--border);
}

/* CARDS */
.card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: var(--space-6);
  box-shadow: var(--shadow);
  transition: all 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* PRODUCT CARDS */
.product-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
  box-shadow: var(--shadow);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.product-image-container {
  position: relative;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.2s;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-info {
  padding: var(--space-4);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--black);
  margin-bottom: var(--space-2);
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--red);
  margin-bottom: var(--space-4);
}

.product-description {
  color: var(--gray);
  margin-bottom: var(--space-4);
  font-size: 0.9rem;
  flex: 1;
}

.product-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: auto;
}

.product-actions .btn {
  flex: 1;
  justify-content: center;
  padding: var(--space-2) var(--space-3);
  font-size: 0.9rem;
}

/* BADGES */
.product-badge {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  background: var(--red);
  color: var(--white);
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.product-badge.featured {
  background: var(--red);
}

.product-badge.discount {
  background: #16a34a;
}

.product-badge.new {
  background: #f59e0b;
}

/* GRID */
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* HERO */
.hero {
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  padding: var(--space-12) 0;
  text-align: center;
  border-radius: 8px;
  margin-bottom: var(--space-8);
}

.hero h1 {
  color: var(--white);
  margin-bottom: var(--space-4);
}

.hero p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-bottom: var(--space-6);
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-logo-img {
  height: 120px;
  margin-bottom: var(--space-4);
}

/* SECTIONS */
.section {
  margin-bottom: var(--space-12);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border);
}

.section-title {
  font-size: 2rem;
  color: var(--black);
  margin: 0;
}

.view-all {
  color: var(--red);
  font-weight: 500;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.view-all:hover {
  color: var(--red-dark);
}

/* FORMS */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--black);
}

.form-input,
input,
textarea,
select {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
  font-family: inherit;
}

.form-input:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--red);
}

/* FILTERS */
.filters {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--gray-light);
  color: var(--gray);
  border: 1px solid var(--border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
}

/* SEARCH */
.search-container {
  position: relative;
  margin-bottom: var(--space-6);
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--red);
}

/* CART */
.cart-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--border);
  border-radius: 8px;
  margin-bottom: var(--space-4);
}

.cart-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.cart-item-info {
  flex: 1;
}

.cart-item-name {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.cart-item-price {
  color: var(--red);
  font-weight: 700;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border);
  background: var(--white);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover {
  background: var(--gray-light);
}

/* FOOTER - MODERN & CLEAN */
.footer {
  background: var(--black);
  color: var(--white);
  padding: var(--space-12) 0 var(--space-8);
  margin-top: var(--space-12);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--red), var(--red-dark));
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-8);
}

.footer-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 320px;
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-section h3 {
  color: var(--white);
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: var(--space-4);
  position: relative;
  padding-bottom: var(--space-2);
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--red);
  border-radius: 1px;
}

.footer-section p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: var(--space-3);
  flex: 1;
}

.footer-section p strong {
  color: var(--white);
  font-weight: 600;
}

/* Ensure all footer sections have consistent content distribution */
.footer-section:nth-child(1) {
  justify-content: flex-start;
}

.footer-section:nth-child(2),
.footer-section:nth-child(3) {
  justify-content: space-between;
}

.footer-section:nth-child(4) {
  justify-content: flex-start;
}

.footer-links {
  list-style: none;
  margin: 0;
  padding: 0;
}

.footer-link {
  margin-bottom: var(--space-2);
}

.footer-link a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) 0;
  font-weight: 500;
}

.footer-link a:hover {
  color: var(--red);
  transform: translateX(4px);
}

.footer-link a::before {
  content: '→';
  opacity: 0;
  transition: opacity 0.2s;
}

.footer-link a:hover::before {
  opacity: 1;
}

/* Social Media Buttons */
.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.social-links a {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 140px;
}

.social-links a:hover {
  background: var(--red);
  color: var(--white);
  transform: translateY(-2px);
  border-color: var(--red);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.social-links a i {
  font-size: 1.3rem;
  width: 20px;
  text-align: center;
}

/* Specific social media colors on hover */
.social-links a[href*="facebook"]:hover {
  background: #1877f2;
  border-color: #1877f2;
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

.social-links a[href*="instagram"]:hover {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  border-color: #e6683c;
  box-shadow: 0 4px 12px rgba(230, 104, 60, 0.3);
}

.social-links a[href*="tiktok"]:hover {
  background: #000000;
  border-color: #000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Social Media Button Text */
.social-links a[href*="facebook"]::after {
  content: 'Facebook';
}

.social-links a[href*="instagram"]::after {
  content: 'Instagram';
}

.social-links a[href*="tiktok"]::after {
  content: 'TikTok';
}

/* Copyright */
.copyright {
  text-align: center;
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

.copyright p:last-child {
  margin-bottom: 0;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Contact Info Styling */
.footer-section p[data-contact] {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  border-radius: 6px;
  transition: background 0.2s;
}

.footer-section p[data-contact]:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Special styling for first footer section (company info) */
.footer-section:first-child {
  position: relative;
}

.footer-section:first-child::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  width: 60px;
  height: 60px;
  background: var(--red);
  border-radius: 50%;
  opacity: 0.1;
}

/* MT-3 utility class */
.mt-3 {
  margin-top: var(--space-4) !important;
}

/* UTILITIES */
.text-center { text-align: center; }
.text-red { color: var(--red); }
.text-gray { color: var(--gray); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-8 { margin-bottom: var(--space-8); }
.mt-4 { margin-top: var(--space-4); }
.mt-8 { margin-top: var(--space-8); }

/* LOADING */
.loading {
  text-align: center;
  padding: var(--space-8);
  color: var(--gray);
}

/* ERROR */
.error {
  background: #fee2e2;
  color: #dc2626;
  padding: var(--space-4);
  border-radius: 6px;
  margin-bottom: var(--space-4);
}

/* SUCCESS */
.success {
  background: #dcfce7;
  color: #16a34a;
  padding: var(--space-4);
  border-radius: 6px;
  margin-bottom: var(--space-4);
}

/* RESPONSIVE */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--space-3);
  }

  .footer-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }

  .footer-section {
    min-height: 280px;
  }

  /* Adjust grid layouts */
  .grid-2 {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }
}

@media (max-width: 768px) {
  /* Header Mobile */
  .header-container {
    padding: var(--space-4) var(--space-4);
  }

  .logo-img {
    height: 50px;
  }

  .nav {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    background: var(--white);
    border-right: 1px solid var(--border);
    transition: left 0.3s ease;
    box-shadow: var(--shadow-lg);
    z-index: 999;
    overflow-y: auto;
    padding-top: var(--space-8);
  }

  .nav.nav-open {
    left: 0;
  }

  /* Mobile Menu Overlay */
  .nav.nav-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }

  .nav-links {
    flex-direction: column;
    padding: var(--space-4);
    gap: var(--space-2);
  }

  .nav-link {
    width: 100%;
    padding: var(--space-4) var(--space-4);
    border-radius: 8px;
    margin-bottom: var(--space-1);
    justify-content: flex-start;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .nav-text {
    font-size: 1.1rem;
  }

  .mobile-actions {
    display: flex;
  }

  .mobile-search {
    display: none;
  }

  /* Other responsive styles */
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: flex-start;
  }

  .hero {
    padding: var(--space-8) var(--space-4);
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }

  .filters {
    justify-content: center;
  }

  .cart-item {
    flex-direction: column;
    text-align: center;
  }

  /* Footer Mobile */
  .footer-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .footer-section {
    text-align: center;
    align-items: center;
    min-height: auto;
    padding: var(--space-6);
  }

  .footer-section h3::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-link a {
    justify-content: center;
  }

  .social-links {
    align-items: center;
    width: 100%;
  }

  .social-links a {
    justify-content: center;
    min-width: 160px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: var(--space-3) var(--space-3);
  }

  .logo-img {
    height: 45px;
  }

  .nav {
    width: 260px;
  }

  .nav-link {
    font-size: 1rem;
    padding: var(--space-3) var(--space-3);
    font-weight: 600;
  }

  .nav-text {
    font-size: 1rem;
  }

  /* Typography for small screens */
  h1 {
    font-size: 1.8rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.3rem;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.1rem;
  }

  /* Button adjustments */
  .btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.9rem;
  }

  .btn-lg {
    padding: var(--space-3) var(--space-4);
    font-size: 1rem;
  }

  /* Form elements */
  input,
  textarea,
  select {
    font-size: 0.9rem;
    padding: var(--space-2);
  }

  /* Cards */
  .card {
    padding: var(--space-3);
  }

  /* Spacing adjustments */
  .section {
    padding: var(--space-6) 0;
  }

  /* Footer */
  .footer-section {
    padding: var(--space-4);
  }

  .social-links a {
    min-width: 140px;
    font-size: 0.9rem;
  }

  /* Grid adjustments */
  .grid-2,
  .grid-3,
  .grid-4 {
    gap: var(--space-3);
  }

  /* Container adjustments */
  .container {
    padding: 0 var(--space-2);
  }
}

/* GLOBAL PAGINATION STYLES */
.pagination-info {
  text-align: center;
  margin: var(--space-4) 0 var(--space-2) 0;
}

.pagination-info p {
  color: var(--gray);
  font-size: 0.9rem;
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-2);
  margin: var(--space-4) 0 var(--space-6) 0;
  padding: var(--space-4) 0;
}

.pagination-button {
  padding: var(--space-3) var(--space-4);
  background: var(--white);
  border: 2px solid var(--border);
  color: var(--gray);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  min-width: 100px;
  justify-content: center;
}

.pagination-button:hover:not(:disabled) {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.pagination-button:disabled {
  background: #f8f9fa;
  color: #adb5bd;
  border-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.6;
}

.pagination-pages {
  display: flex;
  gap: var(--space-1);
  margin: 0 var(--space-3);
}

.pagination-page {
  width: 44px;
  height: 44px;
  padding: 0;
  background: var(--white);
  border: 2px solid var(--border);
  color: var(--gray);
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover:not(.active) {
  background: #f8f9fa;
  border-color: var(--red);
  color: var(--red);
  transform: translateY(-1px);
}

.pagination-page.active {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  transform: translateY(-1px);
}

.pagination-page.active:hover {
  background: var(--red-dark);
  border-color: var(--red-dark);
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: var(--gray);
  font-weight: 600;
  font-size: 0.9rem;
  user-select: none;
}