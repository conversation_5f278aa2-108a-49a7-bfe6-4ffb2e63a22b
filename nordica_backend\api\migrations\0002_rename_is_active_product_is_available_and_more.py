# Generated by Django 5.2.4 on 2025-07-24 13:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='product',
            old_name='is_active',
            new_name='is_available',
        ),
        migrations.RenameField(
            model_name='product',
            old_name='stock_quantity',
            new_name='stock',
        ),
        migrations.RemoveField(
            model_name='product',
            name='image',
        ),
        migrations.RemoveField(
            model_name='productimage',
            name='alt_text',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='date_of_birth',
        ),
        migrations.AddField(
            model_name='category',
            name='is_active',
            field=models.<PERSON><PERSON>anField(default=True),
        ),
        migrations.AddField(
            model_name='category',
            name='slug',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='product',
            name='slug',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='productimage',
            name='is_primary',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='city',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='state',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='zip_code',
            field=models.CharField(blank=True, max_length=20),
        ),
    ]
