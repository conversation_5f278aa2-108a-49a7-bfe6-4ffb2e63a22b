import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ordersAPI } from '../../services/api';

const AdminOrders = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get status filter from URL if present
  const queryParams = new URLSearchParams(location.search);
  const statusFromUrl = queryParams.get('status');

  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Filtering and pagination state
  const [filters, setFilters] = useState({
    status: statusFromUrl || '',
    search: '',
    dateFrom: '',
    dateTo: '',
  });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Order status options
  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);

        // Prepare query parameters
        const params = {
          page,
          search: filters.search,
        };

        // Add status filter if specified
        if (filters.status) {
          params.status = filters.status;
        }

        // Add date filters if specified
        if (filters.dateFrom) {
          params.date_from = filters.dateFrom;
        }

        if (filters.dateTo) {
          params.date_to = filters.dateTo;
        }

        // Fetch orders with filters
        const response = await ordersAPI.getAll(params);
        setOrders(response.data.results || []);

        // Calculate total pages
        const count = response.data.count || 0;
        const pageSize = response.data.page_size || 10;
        setTotalPages(Math.ceil(count / pageSize));

      } catch (err) {
        setError('Failed to load orders');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [page, filters]);

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change

    // Update URL if status filter changes
    if (name === 'status') {
      const newParams = new URLSearchParams(location.search);
      if (value) {
        newParams.set('status', value);
      } else {
        newParams.delete('status');
      }
      navigate({ search: newParams.toString() });
    }
  };

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    // The search is already handled by the useEffect when filters.search changes
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    window.scrollTo(0, 0);
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle status update
  const handleStatusUpdate = async (orderId, newStatus) => {
    try {
      await ordersAPI.updateStatus(orderId, { status: newStatus });

      // Update order in the list
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );

    } catch (err) {
      console.error('Error updating order status:', err);
      alert('Failed to update order status');
    }
  };

  return (
    <div className="admin-orders-page">
      <div className="admin-header">
        <h1>Orders</h1>
      </div>

      <div className="admin-filters">
        <form className="search-form" onSubmit={handleSearch}>
          <input
            type="text"
            name="search"
            placeholder="Search by order ID or customer name..."
            value={filters.search}
            onChange={handleFilterChange}
          />
          <button type="submit" className="btn btn-primary">
            <i className="fas fa-search"></i>
          </button>
        </form>

        <div className="filter-controls">
          <div className="filter-group">
            <label htmlFor="status">Status:</label>
            <select
              id="status"
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              <option value="">All Statuses</option>
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="dateFrom">From:</label>
            <input
              type="date"
              id="dateFrom"
              name="dateFrom"
              value={filters.dateFrom}
              onChange={handleFilterChange}
            />
          </div>

          <div className="filter-group">
            <label htmlFor="dateTo">To:</label>
            <input
              type="date"
              id="dateTo"
              name="dateTo"
              value={filters.dateTo}
              onChange={handleFilterChange}
            />
          </div>
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading orders...</p>
        </div>
      ) : error ? (
        <div className="error-container">
          <p>{error}</p>
          <button onClick={() => window.location.reload()} className="btn btn-primary">
            Try Again
          </button>
        </div>
      ) : orders.length === 0 ? (
        <div className="empty-state">
          <p>No orders found matching your criteria.</p>
          <button
            onClick={() => {
              setFilters({ status: '', search: '', dateFrom: '', dateTo: '' });
              setPage(1);
              navigate('/admin/orders');
            }}
            className="btn btn-outline"
          >
            Clear Filters
          </button>
        </div>
      ) : (
        <>
          <div className="table-responsive">
            <table className="admin-table">
              <thead>
                <tr>
                  <th>Order ID</th>
                  <th>Date</th>
                  <th>Customer</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {orders.map(order => (
                  <tr key={order.id}>
                    <td>#{order.id}</td>
                    <td>{formatDate(order.created_at)}</td>
                    <td>
                      <div className="customer-info">
                        <div>{order.full_name}</div>
                        <div className="customer-email">{order.email}</div>
                      </div>
                    </td>
                    <td>{order.total_price} DT</td>
                    <td>
                      <select
                        className={`status-select status-${order.status}`}
                        value={order.status}
                        onChange={(e) => handleStatusUpdate(order.id, e.target.value)}
                      >
                        {statusOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="actions-cell">
                      <button
                        className="btn-icon"
                        onClick={() => navigate(`/admin/orders/${order.id}`)}
                        aria-label="View order details"
                      >
                        <i className="fas fa-eye"></i> View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination">
              <button
                onClick={() => handlePageChange(page - 1)}
                disabled={page === 1}
                className="pagination-button"
              >
                Previous
              </button>

              <div className="pagination-pages">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(p => p === 1 || p === totalPages || (p >= page - 1 && p <= page + 1))
                  .map(p => (
                    <button
                      key={p}
                      onClick={() => handlePageChange(p)}
                      className={`pagination-page ${p === page ? 'active' : ''}`}
                    >
                      {p}
                    </button>
                  ))}
              </div>

              <button
                onClick={() => handlePageChange(page + 1)}
                disabled={page === totalPages}
                className="pagination-button"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AdminOrders;
