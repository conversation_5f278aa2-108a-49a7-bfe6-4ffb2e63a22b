/* ORDER HISTORY PAGE - ORGANIZED & CLEAN */

/* Page Container */
.order-history-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-4);
  min-height: 100vh;
}

/* Header */
.order-history-header {
  margin-bottom: var(--space-8);
  text-align: center;
  padding: var(--space-8) 0;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
}

.order-history-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--space-3);
  position: relative;
  display: inline-block;
  padding-bottom: var(--space-3);
}

.order-history-header h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.order-history-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Error State */
.order-history-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: var(--space-6);
  margin-bottom: var(--space-8);
  text-align: center;
  box-shadow: var(--shadow);
}

.order-history-error p {
  color: #dc2626;
  margin-bottom: var(--space-4);
  font-size: 1.1rem;
}

/* Loading State */
.order-history-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-light);
  border-top: 4px solid var(--red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--gray);
  font-size: 1.1rem;
}

/* Empty State */
.order-history-empty,
.empty-orders {
  text-align: center;
  padding: var(--space-12);
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: var(--shadow);
  max-width: 600px;
  margin: 0 auto;
}

.empty-icon,
.empty-orders-icon {
  font-size: 4rem;
  color: var(--gray);
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-orders-icon svg {
  width: 4rem;
  height: 4rem;
  color: var(--gray);
}

.order-history-empty h2,
.empty-orders h2 {
  font-size: 2rem;
  color: var(--black);
  margin-bottom: var(--space-4);
  font-weight: 700;
}

.order-history-empty p,
.empty-orders p {
  color: var(--gray);
  font-size: 1.1rem;
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.empty-orders .btn {
  padding: var(--space-3) var(--space-6);
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

/* Orders List */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
  padding: var(--space-6);
}

/* Order History Container */
.order-history-container {
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
  padding: var(--space-6);
}

/* Order Grid */
.order-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-4);
}

.order-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-4);
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.order-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Card Structure */
.card-body {
  padding: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.order-title {
  font-size: 1.3rem;
  color: var(--black);
  margin: 0;
  font-weight: 700;
}

.order-status {
  padding: var(--space-1) var(--space-3);
  background: var(--red);
  color: var(--white);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-title-link {
  margin-bottom: var(--space-2);
}

.card-title {
  font-size: 1.2rem;
  color: var(--black);
  margin: 0;
  font-weight: 600;
}

.card-text {
  margin: 0;
}

.order-date {
  color: var(--gray);
  font-size: 0.9rem;
  margin-bottom: var(--space-3);
  font-weight: 500;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-3);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border);
}

.card-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--red);
}

.order-status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.order-status-badge.delivered {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.order-status-badge.pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.order-status-badge.processing {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.order-status-badge.shipped {
  background: #e0e7ff;
  color: #3730a3;
  border: 1px solid #c7d2fe;
}

.order-status-badge.cancelled {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.card-actions {
  margin-top: var(--space-3);
  display: flex;
  justify-content: flex-end;
}

.card-actions .btn {
  padding: var(--space-2) var(--space-4);
  font-size: 0.9rem;
}

/* Order Items Preview */
.order-items-preview {
  margin: var(--space-4) 0;
}

.items-summary {
  margin-bottom: var(--space-3);
}

.items-count {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
}

.count-icon {
  color: var(--red);
  font-size: 1.1rem;
}

.count-text {
  font-weight: 500;
}

.items-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--gray-light);
  border-radius: 8px;
  border: 1px solid var(--border);
  margin: var(--space-3) 0;
}

.items-list::-webkit-scrollbar {
  width: 4px;
}

.items-list::-webkit-scrollbar-track {
  background: transparent;
}

.items-list::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 2px;
}

.items-list::-webkit-scrollbar-thumb:hover {
  background: var(--gray);
}

.item-preview {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--white);
  border-radius: 6px;
  border: 1px solid var(--border);
  transition: all 0.2s;
}

.item-preview:hover {
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.item-preview::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, var(--red), var(--red-dark));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.item-preview:hover::before {
  opacity: 1;
}

.item-preview::after {
  content: '';
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 8px;
  height: 8px;
  background: var(--red);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.item-preview:hover::after {
  opacity: 0.3;
}

.item-image {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  border: 3px solid var(--white);
  background: linear-gradient(135deg, var(--gray-light) 0%, #e5e7eb 100%);
}

.item-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(220, 38, 38, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.item-preview:hover .item-image::before {
  opacity: 1;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  position: relative;
  z-index: 0;
}

.item-preview:hover .item-image img {
  transform: scale(1.08);
}

.item-image img:not([src]),
.item-image img[src=""],
.item-image img[src="/images/product-placeholder.jpg"] {
  background: var(--gray-light);
  position: relative;
}

.item-image img:not([src])::after,
.item-image img[src=""]::after,
.item-image img[src="/images/product-placeholder.jpg"]::after {
  content: '📦';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  opacity: 0.5;
}

.item-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.item-name {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  line-height: 1.3;
  margin: 0;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-preview:hover .item-name {
  color: var(--red);
}

.item-brand {
  font-size: 0.8rem;
  color: var(--gray);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

.item-sku {
  font-size: 0.75rem;
  color: var(--gray);
  font-family: monospace;
  background: var(--gray-light);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  margin: var(--space-1) 0;
}

.item-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-3);
  margin-top: var(--space-2);
  padding-top: var(--space-2);
  border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.item-quantity {
  color: var(--black);
  font-weight: 600;
  background: linear-gradient(135deg, var(--gray-light), var(--white));
  padding: var(--space-1) var(--space-3);
  border-radius: 16px;
  font-size: 0.85rem;
  border: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.item-quantity:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.item-quantity::before {
  content: '×';
  font-weight: 700;
  color: var(--red);
  font-size: 0.9rem;
}

.item-price {
  color: var(--red);
  font-weight: 700;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(220, 38, 38, 0.05));
  padding: var(--space-1) var(--space-2);
  border-radius: 8px;
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.item-price::after {
  content: 'DT';
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--gray);
  margin-left: var(--space-1);
}

.item-unit-price {
  font-size: 0.75rem;
  color: var(--gray);
  font-weight: 500;
  margin-top: var(--space-1);
  text-align: right;
}

.item-subtotal {
  color: var(--gray);
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: var(--space-1);
  text-align: right;
}

.item-category {
  color: var(--gray);
  font-size: 0.75rem;
  margin-top: var(--space-1);
  font-style: italic;
}

/* Simplified Item Preview */
.item-preview-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  text-align: center;
}

.item-box {
  width: 50px;
  height: 50px;
  background: var(--white);
  border: 2px solid var(--border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--red);
  font-size: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow);
}

.item-box:hover {
  border-color: var(--red);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.item-name-simple {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--black);
  line-height: 1.2;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-quantity-simple {
  font-size: 0.75rem;
  color: var(--gray);
  font-weight: 500;
  background: var(--white);
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid var(--border);
}

.more-items-simple {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: var(--gray);
  font-weight: 500;
  background: var(--white);
  border: 2px dashed var(--border);
  border-radius: 8px;
  padding: var(--space-2);
  min-height: 50px;
  text-align: center;
}

.more-items {
  text-align: center;
  padding: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
  font-style: italic;
}

/* Order Header */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border);
}

.order-info h3 {
  font-size: 1.3rem;
  color: var(--black);
  margin-bottom: var(--space-1);
}

.order-date {
  color: var(--gray);
  font-size: 0.9rem;
  margin-bottom: var(--space-4);
}

/* No Items Message */
.no-items-message {
  text-align: center;
  padding: var(--space-6);
  color: var(--gray);
}

.no-items-icon {
  font-size: 2rem;
  margin-bottom: var(--space-2);
  opacity: 0.5;
}

.no-items-message p {
  margin: 0;
  font-style: italic;
}

/* Order Status */
.order-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.order-status.pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.order-status.processing {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.order-status.shipped {
  background: #e0e7ff;
  color: #3730a3;
  border: 1px solid #c7d2fe;
}

.order-status.delivered {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.order-status.cancelled {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Order Details */
.order-details {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.order-detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.order-detail-label {
  color: var(--gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.order-detail-value {
  color: var(--black);
  font-weight: 600;
}

.order-total {
  color: var(--red);
  font-size: 1.2rem;
  font-weight: 700;
}

/* Order Items */
.order-items {
  margin-bottom: var(--space-4);
}

.order-items h4 {
  color: var(--black);
  margin-bottom: var(--space-3);
  font-size: 1.1rem;
}

.order-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--gray-light);
  border-radius: 6px;
  margin-bottom: var(--space-2);
}

.order-item:last-child {
  margin-bottom: 0;
}

.order-item-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.order-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.order-item-details {
  flex: 1;
}

.order-item-name {
  font-weight: 600;
  color: var(--black);
  margin-bottom: var(--space-1);
}

.order-item-info {
  color: var(--gray);
  font-size: 0.9rem;
}

.order-item-price {
  font-weight: 600;
  color: var(--red);
}

/* Order Actions */
.order-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

.order-action-btn {
  padding: var(--space-2) var(--space-4);
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.9rem;
}

.btn-view {
  background: var(--white);
  color: var(--red);
  border: 1px solid var(--red);
}

.btn-view:hover {
  background: var(--red);
  color: var(--white);
}

.btn-reorder {
  background: var(--red);
  color: var(--white);
  border: 1px solid var(--red);
}

.btn-reorder:hover {
  background: var(--red-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .order-history-page {
    padding: var(--space-4) var(--space-2);
  }

  .order-history-header h1 {
    font-size: 2rem;
  }

  .order-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .order-details {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .order-item {
    flex-direction: column;
    text-align: center;
  }

  .items-list {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: var(--space-2);
    padding: var(--space-3);
  }

  .item-box {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .item-name-simple {
    font-size: 0.75rem;
    max-width: 60px;
  }

  .item-quantity-simple {
    font-size: 0.7rem;
    padding: 1px 4px;
  }

  .item-preview {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }

  .item-image {
    width: 60px;
    height: 60px;
  }

  .item-name {
    white-space: normal;
    text-align: center;
  }

  .item-details {
    justify-content: center;
  }

  .card-meta {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .card-actions {
    justify-content: stretch;
  }

  .card-actions .btn {
    width: 100%;
  }

  .order-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .order-action-btn {
    justify-content: center;
  }

  /* Empty Orders Mobile */
  .empty-orders {
    padding: var(--space-8);
    margin: 0 var(--space-2);
  }

  .empty-orders-icon {
    font-size: 3rem;
    margin-bottom: var(--space-3);
  }

  .empty-orders-icon svg {
    width: 3rem;
    height: 3rem;
  }

  .empty-orders h2 {
    font-size: 1.5rem;
    margin-bottom: var(--space-3);
  }

  .empty-orders p {
    font-size: 1rem;
    margin-bottom: var(--space-4);
  }
}
