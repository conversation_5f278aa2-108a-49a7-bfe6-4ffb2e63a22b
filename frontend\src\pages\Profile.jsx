import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { authAPI } from '../services/api';
import { Link } from 'react-router-dom';
import { FaUser } from 'react-icons/fa';
import { FaShoppingBag } from 'react-icons/fa';
import { FaLock } from 'react-icons/fa';
import { FaBell } from 'react-icons/fa';
import { FaSignOutAlt } from 'react-icons/fa';
import { extractProfileDataForForm } from '../utils/profileUtils';
import '../styles/profile.css';

const Profile = () => {
  const { currentUser, updateProfile, logout } = useAuth();

  // State for active tab
  const [activeTab, setActiveTab] = useState('profile');

  // State for profile form
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
  });

  // State for password change form
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // State for notification preferences
  const [notifications, setNotifications] = useState({
    orderUpdates: true,
    promotions: false,
    newsletter: false,
    productUpdates: true,
  });



  // State for form handling
  const [formErrors, setFormErrors] = useState({});
  const [passwordErrors, setPasswordErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  // Load user data when component mounts
  useEffect(() => {
    if (currentUser) {
      const profileData = extractProfileDataForForm(currentUser);
      setFormData(profileData);
    }
  }, [currentUser]);

  // Handle input change for profile form
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Clear success message when form is changed
    if (success) {
      setSuccess(false);
    }
  };

  // Handle input change for password form
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (passwordErrors[name]) {
      setPasswordErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Clear success message when form is changed
    if (success) {
      setSuccess(false);
    }
  };

  // Handle notification toggle
  const handleNotificationToggle = (name) => {
    setNotifications(prev => ({ ...prev, [name]: !prev[name] }));
  };

  // Validate profile form
  const validateForm = () => {
    const errors = {};

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = 'Phone number is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Validate password form
  const validatePasswordForm = () => {
    const errors = {};

    if (!passwordData.currentPassword) {
      errors.currentPassword = 'Current password is required';
    }

    if (!passwordData.newPassword) {
      errors.newPassword = 'New password is required';
    } else if (passwordData.newPassword.length < 8) {
      errors.newPassword = 'Password must be at least 8 characters';
    }

    if (!passwordData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your new password';
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle profile form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        setLoading(true);
        setError(null);

        // Prepare profile data
        const profileData = {
          user: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
          },
          phone_number: formData.phoneNumber,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          zip_code: formData.zipCode,
        };

        // Update profile
        await updateProfile(profileData);

        setSuccess(true);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(false);
        }, 3000);

      } catch (err) {
        console.error('Error updating profile:', err);
        setError('Failed to update profile. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle password form submission
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    if (validatePasswordForm()) {
      try {
        setLoading(true);
        setError(null);

        // Prepare password data
        const passwordChangeData = {
          old_password: passwordData.currentPassword,
          new_password: passwordData.newPassword,
        };

        // Call API to change password
        await authAPI.changePassword(passwordChangeData);

        // Reset form
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });

        setSuccess(true);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(false);
        }, 3000);

      } catch (err) {
        console.error('Error changing password:', err);

        // Handle specific error messages
        if (err.response?.data?.old_password) {
          setPasswordErrors({ currentPassword: err.response.data.old_password[0] });
        } else if (err.response?.data?.new_password) {
          setPasswordErrors({ newPassword: err.response.data.new_password[0] });
        } else {
          setError('Failed to change password. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle notification preferences submission
  const handleNotificationsSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      // In a real app, you would save notification preferences to the backend
      console.log('Saving notification preferences:', notifications);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setSuccess(true);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);

    } catch (err) {
      console.error('Error saving notification preferences:', err);
      setError('Failed to save notification preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error('Error logging out:', err);
    }
  };

  if (!currentUser) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading profile...</p>
      </div>
    );
  }

  // Render profile tab
  const renderProfile = () => (
    <div className="profile-section">
      <h2>Personal Information</h2>

      <form className="profile-form" onSubmit={handleSubmit}>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="firstName" className="form-label">First Name</label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              className={`form-control ${formErrors.firstName ? 'error' : ''}`}
              value={formData.firstName}
              onChange={handleChange}
            />
            {formErrors.firstName && <div className="form-error">{formErrors.firstName}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="lastName" className="form-label">Last Name</label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              className={`form-control ${formErrors.lastName ? 'error' : ''}`}
              value={formData.lastName}
              onChange={handleChange}
            />
            {formErrors.lastName && <div className="form-error">{formErrors.lastName}</div>}
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="email" className="form-label">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            className={`form-control ${formErrors.email ? 'error' : ''}`}
            value={formData.email}
            onChange={handleChange}
          />
          {formErrors.email && <div className="form-error">{formErrors.email}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="phoneNumber" className="form-label">Phone Number</label>
          <input
            type="tel"
            id="phoneNumber"
            name="phoneNumber"
            className={`form-control ${formErrors.phoneNumber ? 'error' : ''}`}
            value={formData.phoneNumber}
            onChange={handleChange}
          />
          {formErrors.phoneNumber && <div className="form-error">{formErrors.phoneNumber}</div>}
        </div>

        <h2 className="mt-4">Shipping Address</h2>

        <div className="form-group">
          <label htmlFor="address" className="form-label">Address</label>
          <input
            type="text"
            id="address"
            name="address"
            className="form-control"
            value={formData.address}
            onChange={handleChange}
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="city" className="form-label">City</label>
            <input
              type="text"
              id="city"
              name="city"
              className="form-control"
              value={formData.city}
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label htmlFor="state" className="form-label">State</label>
            <input
              type="text"
              id="state"
              name="state"
              className="form-control"
              value={formData.state}
              onChange={handleChange}
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="zipCode" className="form-label">Zip Code</label>
          <input
            type="text"
            id="zipCode"
            name="zipCode"
            className="form-control"
            value={formData.zipCode}
            onChange={handleChange}
          />
        </div>

        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );

  // Render password tab
  const renderPassword = () => (
    <div className="profile-section">
      <h2>Change Password</h2>
      <p className="section-description">
        Update your password to keep your account secure. Your password should be at least 8 characters long.
      </p>

      <form className="profile-form" onSubmit={handlePasswordSubmit}>
        <div className="form-group">
          <label htmlFor="currentPassword" className="form-label">Current Password</label>
          <input
            type="password"
            id="currentPassword"
            name="currentPassword"
            className={`form-control ${passwordErrors.currentPassword ? 'error' : ''}`}
            value={passwordData.currentPassword}
            onChange={handlePasswordChange}
          />
          {passwordErrors.currentPassword && <div className="form-error">{passwordErrors.currentPassword}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="newPassword" className="form-label">New Password</label>
          <input
            type="password"
            id="newPassword"
            name="newPassword"
            className={`form-control ${passwordErrors.newPassword ? 'error' : ''}`}
            value={passwordData.newPassword}
            onChange={handlePasswordChange}
          />
          {passwordErrors.newPassword && <div className="form-error">{passwordErrors.newPassword}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="confirmPassword" className="form-label">Confirm New Password</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            className={`form-control ${passwordErrors.confirmPassword ? 'error' : ''}`}
            value={passwordData.confirmPassword}
            onChange={handlePasswordChange}
          />
          {passwordErrors.confirmPassword && <div className="form-error">{passwordErrors.confirmPassword}</div>}
        </div>

        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Updating...' : 'Update Password'}
          </button>
        </div>
      </form>
    </div>
  );

  // Render notifications tab
  const renderNotifications = () => (
    <div className="profile-section">
      <h2>Notification Preferences</h2>
      <p className="section-description">
        Manage how you receive notifications and updates from Nordica Nutrition.
      </p>

      <form className="profile-form" onSubmit={handleNotificationsSubmit}>
        <div className="notification-options">
          <div className="notification-option">
            <div className="option-details">
              <h3>Order Updates</h3>
              <p>Receive notifications about your order status and delivery updates.</p>
            </div>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="orderUpdates"
                checked={notifications.orderUpdates}
                onChange={() => handleNotificationToggle('orderUpdates')}
              />
              <label htmlFor="orderUpdates"></label>
            </div>
          </div>

          <div className="notification-option">
            <div className="option-details">
              <h3>Promotions and Discounts</h3>
              <p>Get notified about special offers, discounts, and promotions.</p>
            </div>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="promotions"
                checked={notifications.promotions}
                onChange={() => handleNotificationToggle('promotions')}
              />
              <label htmlFor="promotions"></label>
            </div>
          </div>

          <div className="notification-option">
            <div className="option-details">
              <h3>Newsletter</h3>
              <p>Subscribe to our monthly newsletter with nutrition tips and product updates.</p>
            </div>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="newsletter"
                checked={notifications.newsletter}
                onChange={() => handleNotificationToggle('newsletter')}
              />
              <label htmlFor="newsletter"></label>
            </div>
          </div>

          <div className="notification-option">
            <div className="option-details">
              <h3>Product Updates</h3>
              <p>Get notified about new products, sales, and special offers.</p>
            </div>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="productUpdates"
                checked={notifications.productUpdates}
                onChange={() => handleNotificationToggle('productUpdates')}
              />
              <label htmlFor="productUpdates"></label>
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Preferences'}
          </button>
        </div>
      </form>
    </div>
  );

  // Safety check for currentUser
  if (!currentUser) {
    return (
      <div className="profile-page">
        <div className="loading-container">
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="profile-page">
      <div className="profile-header">
        <h1>My Account</h1>
        <p>Manage your personal information and account settings</p>
      </div>

      {error && (
        <div className="alert alert-danger">
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          <p>
            {activeTab === 'profile' && 'Profile updated successfully!'}
            {activeTab === 'password' && 'Password changed successfully!'}
            {activeTab === 'notifications' && 'Notification preferences saved!'}
          </p>
        </div>
      )}

      <div className="profile-container">
        <div className="profile-sidebar">
          <div className="profile-avatar">
            <div className="avatar-placeholder">
              {currentUser.first_name && currentUser.last_name
                ? `${currentUser.first_name.charAt(0)}${currentUser.last_name.charAt(0)}`
                : (currentUser.username || currentUser.email || 'U').substring(0, 2).toUpperCase()}
            </div>
            <h3>{currentUser.first_name ? `${currentUser.first_name} ${currentUser.last_name}` : (currentUser.username || currentUser.email || 'User')}</h3>
            <p className="user-email">{currentUser.email}</p>
          </div>

          <ul className="profile-menu">
            <li
              className={activeTab === 'profile' ? 'active' : ''}
              onClick={() => setActiveTab('profile')}
            >
              <span className="menu-icon"><FaUser /></span> Profile Information
            </li>
            <li
              className={activeTab === 'password' ? 'active' : ''}
              onClick={() => setActiveTab('password')}
            >
              <span className="menu-icon"><FaLock /></span> Change Password
            </li>
            <li>
              <Link to="/orders" className="menu-link">
                <span className="menu-icon"><FaShoppingBag /></span> Order History
              </Link>
            </li>
            <li
              className={activeTab === 'notifications' ? 'active' : ''}
              onClick={() => setActiveTab('notifications')}
            >
              <span className="menu-icon"><FaBell /></span> Notifications
            </li>
            <li onClick={handleLogout} className="logout-item">
              <span className="menu-icon"><FaSignOutAlt /></span> Logout
            </li>
          </ul>
        </div>

        <div className="profile-content">
          {activeTab === 'profile' && renderProfile()}
          {activeTab === 'password' && renderPassword()}
          {activeTab === 'notifications' && renderNotifications()}
        </div>
      </div>
    </div>
  );
};

export default Profile;
