import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { ordersAPI, settingsAPI } from '../services/api';
import api from '../services/api';
import {
  extractProfileDataForCheckout,
  saveGuestProfileData,
  loadGuestProfileData,
  validateProfileCompleteness
} from '../utils/profileUtils';
import '../styles/checkout.css';

// Tunisian states for delivery
const TUNISIAN_STATES = [
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON> Arous', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  'Kai<PERSON><PERSON>', 'Kasser<PERSON>', 'Kébili', '<PERSON><PERSON>', 'Ma<PERSON><PERSON>', 'Manou<PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>fa<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'
];

const Checkout = () => {
  const { cart, clearCart, loading: cartLoading } = useCart();
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    notes: '',
    createAccount: false,
    password: '',
    confirmPassword: '',
  });
  const [formErrors, setFormErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Site settings for tax and delivery
  const [siteSettings, setSiteSettings] = useState({
    tax_percentage: 10.00,
    delivery_fee: 7.00,
    free_delivery_threshold: 100.00
  });
  const [loadingSettings, setLoadingSettings] = useState(true);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => {
      const updatedData = { ...prev, [name]: newValue };

      // Save guest data to localStorage if not authenticated (debounced)
      if (!currentUser && ['fullName', 'email', 'phone', 'address', 'city', 'state'].includes(name)) {
        // Clear existing timeout
        if (window.guestDataSaveTimeout) {
          clearTimeout(window.guestDataSaveTimeout);
        }

        // Set new timeout to save data after user stops typing
        window.guestDataSaveTimeout = setTimeout(() => {
          saveGuestProfileData(updatedData);
        }, 1000); // Save after 1 second of inactivity
      }

      return updatedData;
    });

    // Clear form errors
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Load user data if available (authenticated users) or guest data (guest users)
  useEffect(() => {
    if (currentUser) {
      // Auto-fill with authenticated user data
      const profileData = extractProfileDataForCheckout(currentUser);
      setFormData(prev => ({
        ...prev,
        ...profileData,
      }));
    } else {
      // Auto-fill with saved guest data if available
      const guestData = loadGuestProfileData();
      if (guestData && Object.values(guestData).some(value => value && value.trim())) {
        setFormData(prev => ({
          ...prev,
          ...guestData,
        }));
      }
    }
  }, [currentUser]);

  // Fetch site settings for tax and delivery
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoadingSettings(true);
        const response = await settingsAPI.getSettings();
        setSiteSettings({
          tax_percentage: response.data.tax_percentage,
          delivery_fee: response.data.delivery_fee,
          free_delivery_threshold: response.data.free_delivery_threshold
        });
      } catch (err) {
        console.error('Error fetching site settings:', err);
        // Keep default values if there's an error
      } finally {
        setLoadingSettings(false);
      }
    };

    fetchSettings();
  }, []);

  // Handle input change (updated to use the new handleInputChange function)
  const handleChange = handleInputChange;

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.fullName.trim()) {
      errors.fullName = 'Full name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required';
    }

    if (!formData.address.trim()) {
      errors.address = 'Address is required';
    }

    if (!formData.city.trim()) {
      errors.city = 'City is required';
    }

    if (!formData.state) {
      errors.state = 'State is required';
    }

    // Validate account creation fields if user wants to create an account
    if (formData.createAccount && !currentUser) {
      if (!formData.password) {
        errors.password = 'Password is required to create an account';
      } else if (formData.password.length < 6) {
        errors.password = 'Password must be at least 6 characters long';
      }

      if (!formData.confirmPassword) {
        errors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Calculate subtotal (before tax and delivery)
  const calculateSubtotal = () => {
    if (!cart?.items || !Array.isArray(cart.items)) return 0;
    return cart.items.reduce((sum, item) => {
      // Add null check for item
      if (!item) return sum;

      // Handle both guest cart format and server format for pack detection
      const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);

      if (isPackItem) {
        // Handle pack items
        const price = parseFloat(item.pack.pack_price || 0);
        const quantity = parseInt(item.quantity || 0, 10);
        return sum + (price * quantity);
      } else if (item.product) {
        // Handle regular product items
        const price = parseFloat(item.product.price || 0);
        const quantity = parseInt(item.quantity || 0, 10);
        return sum + (price * quantity);
      }
      return sum;
    }, 0);
  };

  // Calculate tax amount
  const calculateTax = (subtotal) => {
    try {
      if (!siteSettings || !siteSettings.tax_percentage) {
        console.warn('Tax percentage is not available, using default 10%');
        return (subtotal * 10) / 100;
      }
      // Convert tax_percentage to number if it's a string
      const taxPercentage = parseFloat(siteSettings.tax_percentage);
      if (isNaN(taxPercentage)) {
        console.warn('Invalid tax percentage, using default 10%');
        return (subtotal * 10) / 100;
      }
      return (subtotal * taxPercentage) / 100;
    } catch (error) {
      console.error('Error calculating tax:', error);
      return (subtotal * 10) / 100; // Default to 10% if there's an error
    }
  };

  // Calculate delivery fee
  const calculateDeliveryFee = (subtotal) => {
    try {
      // Check if settings are available
      if (!siteSettings) {
        console.warn('Delivery settings not available, using default 7 DT');
        return 7;
      }

      // Convert threshold to number if it's a string
      const threshold = parseFloat(siteSettings.free_delivery_threshold);

      // Check if order qualifies for free delivery
      if (!isNaN(threshold) && threshold > 0 && subtotal >= threshold) {
        return 0;
      }

      // Convert delivery fee to number if it's a string
      const deliveryFee = parseFloat(siteSettings.delivery_fee);

      // Return delivery fee or default to 7 DT if not available or invalid
      return !isNaN(deliveryFee) ? deliveryFee : 7;
    } catch (error) {
      console.error('Error calculating delivery fee:', error);
      return 7; // Default to 7 DT if there's an error
    }
  };

  // Calculate order total (including tax and delivery)
  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    const deliveryFee = calculateDeliveryFee(subtotal);
    return subtotal + tax + deliveryFee;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!cart?.items || cart.items.length === 0) {
      setError('Your cart is empty');
      return;
    }



    if (validateForm()) {
      try {
        setLoading(true);
        setError(null);

        // Calculate pricing
        const subtotal = calculateSubtotal();
        const taxAmount = calculateTax(subtotal);
        const deliveryFee = calculateDeliveryFee(subtotal);
        const total = subtotal + taxAmount + deliveryFee;

        // Ensure values don't exceed max_digits (10 digits including 2 decimal places)
        const MAX_VALUE = 99999999.99;

        // Format values to ensure they have at most 8 digits before decimal and 2 after
        const formatDecimal = (value) => {
          // Convert to string with 2 decimal places
          const formatted = parseFloat(value).toFixed(2);
          // If the value is too large, cap it
          if (parseFloat(formatted) > MAX_VALUE) {
            return MAX_VALUE.toFixed(2);
          }
          return formatted;
        };

        const safeSubtotal = formatDecimal(subtotal);
        const safeTaxAmount = formatDecimal(taxAmount);
        const safeDeliveryFee = formatDecimal(deliveryFee);
        const safeTotal = formatDecimal(total);

        // Create order items - handle packs as single items using a placeholder product approach
        const orderItems = [];

        for (const item of cart?.items || []) {
          const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);

          if (isPackItem && item.pack) {
            // WORKAROUND: Store pack by name using a placeholder product approach
            console.log('🎁 Processing pack as single item:', item.pack.name);

            // Find the first product in the pack to use as a placeholder (required by backend)
            let placeholderProductId = null;

            if (item.pack.pack_products && item.pack.pack_products.length > 0) {
              const firstPackProduct = item.pack.pack_products[0];
              if (firstPackProduct.product) {
                placeholderProductId = firstPackProduct.product.id;
              }
            }

            if (placeholderProductId) {
              orderItems.push({
                product: placeholderProductId, // Required by backend (placeholder)
                quantity: parseInt(item.quantity) || 1,
                price: formatDecimal(item.pack.pack_price || item.pack.price),
                // Pack identification - these fields will be used for display
                pack_id: item.pack.id,
                pack_name: item.pack.name,
                pack_price: formatDecimal(item.pack.pack_price || item.pack.price),
                is_pack: true,
                item_type: 'pack',
                // Override product name with pack name for better identification
                product_name: item.pack.name, // This will make it show as pack name
                name: item.pack.name
              });
              console.log(`🎁 Added pack "${item.pack.name}" x${item.quantity} at ${item.pack.pack_price} TND`);
            } else {
              console.error('❌ Cannot process pack - no products found:', item.pack.name);
            }
          } else if (item.product) {
            // Regular product item
            orderItems.push({
              product: item.product.id,
              quantity: parseInt(item.quantity) || 1,
              price: formatDecimal(item.product.price),
              item_type: 'product'
            });
            console.log(`📦 Added regular product: ${item.product.name} x${item.quantity}`);
          }
        }

        // Create pack metadata for notes (to preserve pack names)
        const packItems = orderItems.filter(item => item.is_pack);
        let packMetadata = '';
        if (packItems.length > 0) {
          packMetadata = '\n\n--- PACK ITEMS ---\n' +
            packItems.map(item => `Item ID will be replaced with: "${item.pack_name}" (Pack ID: ${item.pack_id})`).join('\n');
        }

        // Create comprehensive order data
        const orderData = {
          total_amount: safeTotal,
          shipping_address: `${formData.fullName}\n${formData.address}\n${formData.city}, ${formData.state}\nEmail: ${formData.email}`,
          phone_number: formData.phone,
          notes: (formData.notes || '') + packMetadata, // Include pack metadata in notes
          // Customer information
          customer_name: formData.fullName,
          customer_email: formData.email,
          customer_phone: formData.phone,
          delivery_address: formData.address,
          delivery_city: formData.city,
          delivery_state: formData.state,
          // Order items with multiple field names for compatibility
          items: orderItems,
          order_items: orderItems,
          orderItems: orderItems,
          // Additional pricing breakdown
          subtotal: safeSubtotal,
          tax_amount: safeTaxAmount,
          delivery_fee: safeDeliveryFee
        };

        // Validate order items
        if (orderItems.length === 0) {
          setError('Unable to process order: No valid items found in cart');
          return;
        }

        // Debug logging for order creation
        console.log('🛒 Creating order with items:', orderItems);
        console.log('📦 Pack items in order:', orderItems.filter(item => item.item_type === 'pack'));
        console.log('📦 Product items in order:', orderItems.filter(item => item.item_type === 'product'));

        try {
          // If user wants to create an account and is not already authenticated
          if (formData.createAccount && !currentUser) {
            try {
              // Import authAPI here to avoid circular imports
              const { authAPI } = await import('../services/api');

              // Create account first
              const [firstName, ...lastNameParts] = formData.fullName.split(' ');
              const lastName = lastNameParts.join(' ') || '';

              await authAPI.register({
                username: formData.email,
                email: formData.email,
                password: formData.password,
                first_name: firstName,
                last_name: lastName,
              });

              console.log('Account created successfully during checkout');
            } catch (accountError) {
              console.error('Error creating account:', accountError);
              // Don't fail the order if account creation fails
              setError('Order will be processed, but account creation failed. You can create an account later.');
            }
          }

          // Create order
          const response = await ordersAPI.create(orderData);

          // Debug the response
          console.log('✅ Order created response:', response.data);
          console.log('📦 Items in created order:', response.data.items);
          console.log('📊 Items count in response:', response.data.items?.length || 0);

          // Store pack mapping for this order in localStorage
          if (response.data && response.data.id) {
            const packMapping = {};
            orderItems.forEach((item, index) => {
              if (item.is_pack) {
                // Map the order item (by position/product) to pack info
                packMapping[`${response.data.id}_${item.product}_${item.quantity}_${item.price}`] = {
                  pack_id: item.pack_id,
                  pack_name: item.pack_name,
                  pack_price: item.pack_price,
                  is_pack: true
                };
              }
            });

            if (Object.keys(packMapping).length > 0) {
              // Store in localStorage for later retrieval
              const existingMappings = JSON.parse(localStorage.getItem('orderPackMappings') || '{}');
              existingMappings[response.data.id] = packMapping;
              localStorage.setItem('orderPackMappings', JSON.stringify(existingMappings));
              console.log('📦 Stored pack mappings for order:', response.data.id, packMapping);
            }
          }

          // Check if order items were created successfully
          if (!response.data.items || response.data.items.length === 0) {
            console.error('⚠️ Order created but no items returned!');
            console.error('📤 Sent items:', orderItems);
            console.error('📥 Received items:', response.data.items);

            // This indicates a backend issue, but the order was still created
            console.warn('⚠️ Order created successfully but items may not be saved properly');
          } else {
            console.log('✅ Order created with items:', response.data.items.length);
          }

          // Clear cart after successful order
          await clearCart();

          // Navigate to order confirmation page
          navigate(`/orders/${response.data.id}`, {
            state: {
              orderConfirmation: true,
              orderData: response.data,
              isGuestOrder: !currentUser,
              customerEmail: formData.email
            }
          });
        } catch (error) {
          console.error('Error creating order:', error);
          if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
            throw new Error(`Server error: ${JSON.stringify(error.response.data)}`);
          } else if (error.request) {
            console.error('No response received:', error.request);
            throw new Error('No response from server. Please check your connection.');
          } else {
            console.error('Error message:', error.message);
            throw error;
          }
        }

      } catch (err) {
        console.error('Checkout error:', err);

        // Display more specific error messages
        if (err.response && err.response.data) {
          // Handle specific field errors
          if (typeof err.response.data === 'object') {
            const errorMessages = [];

            // Check for field-specific errors
            Object.entries(err.response.data).forEach(([field, errors]) => {
              if (Array.isArray(errors)) {
                errorMessages.push(`${field}: ${errors.join(', ')}`);
              } else if (typeof errors === 'string') {
                errorMessages.push(`${field}: ${errors}`);
              }
            });

            // If we have specific error messages, display them
            if (errorMessages.length > 0) {
              setError(`Order failed: ${errorMessages.join('; ')}`);
            } else if (err.response.data.detail) {
              setError(`Order failed: ${err.response.data.detail}`);
            } else {
              setError('Failed to place order. Please check your information and try again.');
            }
          } else {
            setError(`Order failed: ${err.response.data}`);
          }
        } else if (err.message) {
          setError(`Order failed: ${err.message}`);
        } else {
          setError('Failed to place order. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    }
  };

  if (cartLoading || loadingSettings || !cart) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading checkout...</p>
      </div>
    );
  }

  // Redirect to cart if empty
  if ((!cart?.items || cart.items.length === 0) && !cartLoading) {
    return (
      <div className="empty-checkout">
        <h2>Your cart is empty</h2>
        <p>Add some products to your cart before proceeding to checkout.</p>
        <button
          onClick={() => navigate('/products')}
          className="btn btn-primary"
        >
          Browse Products
        </button>
      </div>
    );
  }

  return (
    <div className="checkout-page">
      <div className="checkout-header">
        <h1>Secure Checkout</h1>
        <p>Complete your order safely and securely</p>
      </div>

      <div className="container">
        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        <div className="checkout-container">
          <div className="checkout-form-container">
            <h2>
              {currentUser ? 'Shipping Information' : 'Guest Checkout'}
            </h2>

            {!currentUser && (
              <div className="guest-checkout-notice">
                <p>
                  <strong>Shopping as a guest?</strong> You can complete your purchase without creating an account.
                  Optionally, create an account below for faster future checkouts.
                </p>
                {(() => {
                  const validation = validateProfileCompleteness(formData);
                  if (validation.completionPercentage > 0) {
                    return (
                      <div className="profile-completion-indicator">
                        <div className="completion-bar">
                          <div
                            className="completion-fill"
                            style={{ width: `${validation.completionPercentage}%` }}
                          ></div>
                        </div>
                        <span className="completion-text">
                          Profile {validation.completionPercentage}% complete
                        </span>
                      </div>
                    );
                  }
                  return null;
                })()}
              </div>
            )}

            <form className="checkout-form" onSubmit={handleSubmit}>
              <div className="form-group">
              <label htmlFor="fullName" className="form-label">Full Name</label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                className={`form-control ${formErrors.fullName ? 'error' : ''}`}
                value={formData.fullName}
                onChange={handleChange}
                placeholder="Enter your full name"
              />
              {formErrors.fullName && <div className="form-error">{formErrors.fullName}</div>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  className={`form-control ${formErrors.email ? 'error' : ''}`}
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter your email"
                />
                {formErrors.email && <div className="form-error">{formErrors.email}</div>}
              </div>

              <div className="form-group">
                <label htmlFor="phone" className="form-label">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className={`form-control ${formErrors.phone ? 'error' : ''}`}
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Enter your phone number"
                />
                {formErrors.phone && <div className="form-error">{formErrors.phone}</div>}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="address" className="form-label">Address</label>
              <input
                type="text"
                id="address"
                name="address"
                className={`form-control ${formErrors.address ? 'error' : ''}`}
                value={formData.address}
                onChange={handleChange}
                placeholder="Enter your street address"
              />
              {formErrors.address && <div className="form-error">{formErrors.address}</div>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="city" className="form-label">City</label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  className={`form-control ${formErrors.city ? 'error' : ''}`}
                  value={formData.city}
                  onChange={handleChange}
                  placeholder="Enter your city"
                />
                {formErrors.city && <div className="form-error">{formErrors.city}</div>}
              </div>

              <div className="form-group">
                <label htmlFor="state" className="form-label">State</label>
                <select
                  id="state"
                  name="state"
                  className={`form-control ${formErrors.state ? 'error' : ''}`}
                  value={formData.state}
                  onChange={handleChange}
                >
                  <option value="">Select a state</option>
                  {TUNISIAN_STATES.map(state => (
                    <option key={state} value={state}>{state}</option>
                  ))}
                </select>
                {formErrors.state && <div className="form-error">{formErrors.state}</div>}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="notes" className="form-label">Order Notes (Optional)</label>
              <textarea
                id="notes"
                name="notes"
                className="form-control"
                value={formData.notes}
                onChange={handleChange}
                placeholder="Add any special instructions or notes about your order"
                rows="3"
              ></textarea>
            </div>

            {/* Optional Account Creation for Guest Users */}
            {!currentUser && (
              <div className="account-creation-section">
                <div className="form-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="createAccount"
                      checked={formData.createAccount}
                      onChange={(e) => setFormData(prev => ({ ...prev, createAccount: e.target.checked }))}
                    />
                    <span className="checkmark"></span>
                    Create an account for faster future checkouts
                  </label>
                  <p className="form-help-text">
                    Save your information and track your orders easily
                  </p>
                </div>

                {formData.createAccount && (
                  <div className="account-fields">
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="password" className="form-label">Password</label>
                        <input
                          type="password"
                          id="password"
                          name="password"
                          className={`form-control ${formErrors.password ? 'error' : ''}`}
                          value={formData.password}
                          onChange={handleChange}
                          placeholder="Enter a password (min. 6 characters)"
                        />
                        {formErrors.password && <div className="form-error">{formErrors.password}</div>}
                      </div>

                      <div className="form-group">
                        <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
                        <input
                          type="password"
                          id="confirmPassword"
                          name="confirmPassword"
                          className={`form-control ${formErrors.confirmPassword ? 'error' : ''}`}
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          placeholder="Confirm your password"
                        />
                        {formErrors.confirmPassword && <div className="form-error">{formErrors.confirmPassword}</div>}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="payment-info">
              <h3>Payment Method</h3>
              <div className="payment-methods-container">
                <div className="payment-method selected">
                  <label className="radio-label">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="cashOnDelivery"
                      checked
                      readOnly
                    />
                    Cash on Delivery
                  </label>
                  <p className="payment-note">
                    <i className="fas fa-info-circle"></i>
                    You will pay in cash when the order is delivered to your address.
                  </p>
                </div>
              </div>
            </div>

            <button
              type="submit"
              className="btn btn-primary btn-block place-order-btn"
              disabled={loading}
            >
              {loading ? 'Processing...' : 'Place Order'}
            </button>
            </form>
          </div>

          {/* Order Summary Sidebar */}
          <div className="checkout-order-summary-sidebar">
            <div className="checkout-order-summary">
              <h3>Order Summary</h3>
              <div className="checkout-items-list">
                {cart?.items?.map((item, index) => {
                  const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);

                  return (
                    <div key={index} className="checkout-item">
                      <div className="checkout-item-info">
                        <div className="checkout-item-image">
                          {isPackItem ? (
                            item.pack.image ? (
                              <img src={item.pack.image} alt={item.pack.name} />
                            ) : (
                              <div className="checkout-item-placeholder">📦</div>
                            )
                          ) : (
                            item.product.images && item.product.images.length > 0 ? (
                              <img src={item.product.images[0].image} alt={item.product.name} />
                            ) : (
                              <div className="checkout-item-placeholder">📦</div>
                            )
                          )}
                        </div>
                        <div className="checkout-item-details">
                          <h4 className="checkout-item-name">
                            {isPackItem ? item.pack.name : item.product.name}
                          </h4>
                          <p className="checkout-item-category">
                            {isPackItem ? item.pack.category_name || 'Pack' : item.product.category_name}
                          </p>
                        </div>
                      </div>
                      <div className="checkout-item-pricing">
                        <div className="checkout-item-quantity">Qty: {item.quantity}</div>
                        <div className="checkout-item-price">
                          {isPackItem
                            ? `${parseFloat(item.pack.pack_price || 0).toFixed(2)} TND`
                            : `${parseFloat(item.product.price || 0).toFixed(2)} TND`
                          }
                        </div>
                        <div className="checkout-item-total">
                          {isPackItem
                            ? `${(parseFloat(item.pack.pack_price || 0) * item.quantity).toFixed(2)} TND`
                            : `${(parseFloat(item.product.price || 0) * item.quantity).toFixed(2)} TND`
                          }
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="checkout-summary-totals">
                <div className="checkout-summary-row">
                  <span>Subtotal:</span>
                  <span>{calculateSubtotal().toFixed(2)} DT</span>
                </div>
                <div className="checkout-summary-row">
                  <span>Tax ({((siteSettings.tax_percentage || 0) * 100).toFixed(0)}%):</span>
                  <span>{calculateTax(calculateSubtotal()).toFixed(2)} DT</span>
                </div>
                <div className="checkout-summary-row">
                  <span>Delivery:</span>
                  {calculateDeliveryFee(calculateSubtotal()) > 0 ? (
                    <span>{calculateDeliveryFee(calculateSubtotal()).toFixed(2)} DT</span>
                  ) : (
                    <span className="free-delivery">Free</span>
                  )}
                </div>
              </div>
            </div>

            <div className="order-total-summary">
              <div className="order-total-row">
                <span>Total:</span>
                <span>{calculateTotal().toFixed(2)} DT</span>
              </div>
              <p className="delivery-note">
                <i className="fas fa-info-circle"></i>
                {calculateDeliveryFee(calculateSubtotal()) > 0 ? (
                  <>Includes {calculateDeliveryFee(calculateSubtotal()).toFixed(2)} DT delivery fee and {calculateTax(calculateSubtotal()).toFixed(2)} DT tax</>
                ) : (
                  <>Includes free delivery and {calculateTax(calculateSubtotal()).toFixed(2)} DT tax</>
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
