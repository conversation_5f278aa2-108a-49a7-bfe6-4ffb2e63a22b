import { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import '../styles/auth.css';

const Login = () => {
  const { login, loading, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the redirect path and message from location state or default to home
  const from = location.state?.from || '/';
  const redirectMessage = location.state?.message;

  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState({});

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.username.trim()) {
      errors.username = 'Username or Email is required';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        console.log('Login attempt with:', { username: formData.username, password: '******' });

        // Clear any previous errors
        setFormErrors({});

        await login(formData.username, formData.password);
        console.log('Login successful, navigating to:', from);
        navigate(from);
      } catch (err) {
        console.error('Login error:', err);
        console.error('Error response data:', err.response?.data);

        // Handle specific error messages from the API
        if (err.response?.data) {
          const apiErrors = err.response.data;

          if (apiErrors.non_field_errors) {
            setFormErrors({ general: apiErrors.non_field_errors[0] });
          } else if (apiErrors.username) {
            setFormErrors({ username: Array.isArray(apiErrors.username) ? apiErrors.username[0] : apiErrors.username });
          } else if (apiErrors.password) {
            setFormErrors({ password: Array.isArray(apiErrors.password) ? apiErrors.password[0] : apiErrors.password });
          } else if (apiErrors.detail) {
            setFormErrors({ general: apiErrors.detail });
          } else {
            // Try to extract error messages from the response
            const errorFields = Object.keys(apiErrors);
            if (errorFields.length > 0) {
              const firstField = errorFields[0];
              const firstError = apiErrors[firstField];
              const errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
              setFormErrors({ general: `${firstField}: ${errorMessage}` });
            } else {
              setFormErrors({ general: 'Login failed. Please try again.' });
            }
          }
        } else {
          setFormErrors({ general: 'Login failed. Please try again.' });
        }
      }
    }
  };

  const [showPassword, setShowPassword] = useState(false);



  return (
    <div className="auth-page">
      <div className="auth-container">
        <div className="auth-header">
          <div className="auth-logo">Nordica Nutrition</div>
          <h2 className="auth-title">Welcome Back</h2>
          <p className="auth-subtitle">Enter your credentials to access your account</p>
        </div>

        {redirectMessage && (
          <div className="info-container">
            <p>{redirectMessage}</p>
          </div>
        )}

        {error && (
          <div className="error-container">
            <p>{error}</p>
          </div>
        )}

        {formErrors.general && (
          <div className="error-container">
            <p>{formErrors.general}</p>
          </div>
        )}

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="username" className="form-label">Username or Email</label>
            <input
              type="text"
              id="username"
              name="username"
              className={`form-control ${formErrors.username ? 'error' : ''}`}
              value={formData.username}
              onChange={handleChange}
              placeholder="Enter your username or email"
            />
            {formErrors.username && <div className="form-error">{formErrors.username}</div>}
          </div>

          <div className="form-group" style={{ position: 'relative' }}>
            <label htmlFor="password" className="form-label">Password</label>
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              className={`form-control ${formErrors.password ? 'error' : ''}`}
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter your password"
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
            </button>
            {formErrors.password && <div className="form-error">{formErrors.password}</div>}
          </div>

          <div className="forgot-password">
            <Link to="/forgot-password">Forgot password?</Link>
          </div>

          <div className="form-check">
            <input type="checkbox" id="remember" name="remember" />
            <label htmlFor="remember">Remember me</label>
          </div>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Signing In...' : 'Sign In'}
          </button>
        </form>



        <div className="auth-footer">
          <p>Don't have an account? <Link to="/register">Sign Up</Link></p>
        </div>
      </div>
    </div>
  );
};

export default Login;
