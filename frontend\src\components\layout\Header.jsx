import { useState, useEffect, useCallback, useRef } from 'react';
import { Link, NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useCart } from '../../contexts/CartContext';
import { productsAPI, packsAPI } from '../../services/api';
import '../../styles/header.css';
import {
  FaHome,
  FaBoxes,
  FaShoppingBag,
  FaSearch,
  FaInfoCircle,
  FaEnvelope,
  FaUser,
  FaSignOutAlt,
  FaSignInAlt,
  FaUserPlus,
  FaShoppingCart,
  FaBars,
  FaTimes,
  FaSpinner,
  FaGift
} from 'react-icons/fa';

const Header = () => {
  const { currentUser, isAuthenticated, isAdmin, logout } = useAuth();
  const { itemCount } = useCart();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isToggling, setIsToggling] = useState(false);
  const navigate = useNavigate();

  // Search functionality state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchExpanded, setSearchExpanded] = useState(false);
  const searchRef = useRef(null);
  const searchTimeoutRef = useRef(null);

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      closeMobileMenu(); // Close mobile menu on logout
      navigate('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Toggle mobile menu with immediate response
  const toggleMobileMenu = useCallback((e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Prevent rapid clicking with shorter debounce
    if (isToggling) return;

    setIsToggling(true);

    // Use functional update to ensure we get the latest state
    setMobileMenuOpen(prevState => {
      const newState = !prevState;
      console.log('🍔 Toggling menu from', prevState, 'to', newState);

      // Close search when opening mobile menu
      if (newState) {
        setSearchExpanded(false);
        clearSearch();
      }

      return newState;
    });

    // Reset toggling state after a shorter delay
    setTimeout(() => setIsToggling(false), 50);
  }, [isToggling]);

  // Close mobile menu when clicking on a link
  const closeMobileMenu = useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  // Close mobile menu when clicking outside or pressing Escape
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (mobileMenuOpen && !isToggling) {
        const nav = document.querySelector('.nav');
        const menuBtn = document.querySelector('.mobile-menu-btn');

        if (nav && !nav.contains(event.target) &&
            menuBtn && !menuBtn.contains(event.target)) {
          setMobileMenuOpen(false);
        }
      }
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    // Use capture phase for more reliable detection
    document.addEventListener('click', handleClickOutside, true);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('click', handleClickOutside, true);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [mobileMenuOpen, isToggling]);

  // Search functionality
  const performSearch = async (query) => {
    if (!query || query.trim().length < 2) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setSearchLoading(true);
    try {
      // Search both products and packs
      const [productsResponse, packsResponse] = await Promise.all([
        productsAPI.getAll({ search: query.trim() }),
        packsAPI.getAll({ search: query.trim() })
      ]);

      const products = productsResponse.data.results || [];
      const packs = packsResponse.data.results || packsResponse.data || [];

      // Combine results with type indicator
      const productResults = products.slice(0, 3).map(product => ({
        ...product,
        type: 'product'
      }));

      const packResults = packs.slice(0, 2).map(pack => ({
        ...pack,
        type: 'pack'
      }));

      const combinedResults = [...productResults, ...packResults];
      setSearchResults(combinedResults);
      setShowSearchResults(true);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Debounce search
    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query);
    }, 300);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setShowSearchResults(false);
      setSearchQuery('');
      closeMobileMenu();
    }
  };

  const handleSearchResultClick = (itemId, itemType = 'product') => {
    if (itemType === 'pack') {
      navigate(`/packs/${itemId}`);
    } else {
      navigate(`/products/${itemId}`);
    }
    setShowSearchResults(false);
    setSearchQuery('');
    closeMobileMenu();
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowSearchResults(false);
  };

  const toggleSearchExpanded = () => {
    setSearchExpanded(!searchExpanded);
    if (!searchExpanded) {
      // Focus input when expanding
      setTimeout(() => {
        const input = searchRef.current?.querySelector('.search-input');
        if (input) input.focus();
      }, 100);
    } else {
      // Clear search when collapsing
      clearSearch();
    }
  };

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className={`header ${scrolled ? 'scrolled' : ''}`}>
      <div className="header-container">
        {/* Mobile Top Bar */}
        <div className="mobile-top-bar">
          {/* Mobile Menu Button - Top Left */}
          <button
            className="mobile-menu-btn"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
            type="button"
            disabled={isToggling}
          >
            {mobileMenuOpen ? <FaTimes /> : <FaBars />}
          </button>

          {/* Mobile Logo - Center */}
          <div className="mobile-logo">
            <Link to="/" className="logo-link">
              <img src="/assets/logo/logo.png" alt="Nordica Nutrition" className="logo-img" />
            </Link>
          </div>

          {/* Mobile Actions - Top Right */}
          <div className="mobile-actions">
            {/* Profile/Login Icon */}
            {isAuthenticated ? (
              <Link to="/profile" className="mobile-action-btn" title="Profile">
                <FaUser />
              </Link>
            ) : (
              <Link to="/login" className="mobile-action-btn" title="Login">
                <FaSignInAlt />
              </Link>
            )}

            {/* Cart Icon */}
            <Link to="/cart" className="mobile-action-btn cart-link" title="Cart">
              <div className="cart-icon-wrapper">
                <FaShoppingCart />
                {itemCount > 0 && (
                  <span className="cart-badge">{itemCount}</span>
                )}
              </div>
            </Link>
          </div>
        </div>

        {/* Desktop Logo - Hidden on Mobile */}
        <div className="logo-centered desktop-only">
          <Link to="/" className="logo-link">
            <img src="/assets/logo/logo.png" alt="Nordica Nutrition" className="logo-img" />
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className={`nav ${mobileMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-links">
            {/* Home */}
            <li>
              <NavLink
                to="/"
                className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                title="Home"
                onClick={closeMobileMenu}
              >
                <FaHome />
                <span className="nav-text">Home</span>
              </NavLink>
            </li>

            {/* Products */}
            <li>
              <NavLink
                to="/products"
                className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                title="Products"
                onClick={closeMobileMenu}
              >
                <FaBoxes />
                <span className="nav-text">Products</span>
              </NavLink>
            </li>

            {/* Packs */}
            <li>
              <NavLink
                to="/packs"
                className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                title="Packs"
                onClick={closeMobileMenu}
              >
                <FaGift />
                <span className="nav-text">Packs</span>
              </NavLink>
            </li>

            {/* Orders - only for authenticated users */}
            {isAuthenticated && (
              <li>
                <NavLink
                  to="/orders"
                  className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                  title="Orders"
                  onClick={closeMobileMenu}
                >
                  <FaShoppingBag />
                  <span className="nav-text">Orders</span>
                </NavLink>
              </li>
            )}

            {/* About Us */}
            <li>
              <NavLink
                to="/about"
                className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                title="About Us"
                onClick={closeMobileMenu}
              >
                <FaInfoCircle />
                <span className="nav-text">About Us</span>
              </NavLink>
            </li>

            {/* Contact */}
            <li>
              <NavLink
                to="/contact"
                className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                title="Contact"
                onClick={closeMobileMenu}
              >
                <FaEnvelope />
                <span className="nav-text">Contact</span>
              </NavLink>
            </li>

            {/* Profile - only for authenticated users */}
            {isAuthenticated && (
              <li>
                <NavLink
                  to="/profile"
                  className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                  title="Profile"
                  onClick={closeMobileMenu}
                >
                  <FaUser />
                  <span className="nav-text">Profile</span>
                </NavLink>
              </li>
            )}

            {/* Cart icon - moved after profile */}
            <li>
              <NavLink
                to="/cart"
                className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                title="Cart"
                onClick={closeMobileMenu}
              >
                <div className="icon-with-badge">
                  <FaShoppingCart />
                  {itemCount > 0 && <span className="badge">{itemCount}</span>}
                </div>
                <span className="nav-text">Cart</span>
              </NavLink>
            </li>

            {/* Admin - only for admin users */}
            {isAdmin && (
              <li>
                <NavLink
                  to="/admin"
                  className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                  title="Admin"
                  onClick={closeMobileMenu}
                >
                  <FaUser className="admin-icon" />
                  <span className="nav-text">Admin</span>
                </NavLink>
              </li>
            )}

            {/* Authentication */}
            {isAuthenticated ? (
              <li>
                <button className="nav-link logout-btn" onClick={handleLogout} title="Logout">
                  <FaSignOutAlt />
                  <span className="nav-text">Logout</span>
                </button>
              </li>
            ) : (
              <>
                <li>
                  <NavLink
                    to="/login"
                    className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                    title="Login"
                    onClick={closeMobileMenu}
                  >
                    <FaSignInAlt />
                    <span className="nav-text">Login</span>
                  </NavLink>
                </li>
                <li>
                  <NavLink
                    to="/register"
                    className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
                    title="Register"
                    onClick={closeMobileMenu}
                  >
                    <FaUserPlus />
                    <span className="nav-text">Register</span>
                  </NavLink>
                </li>
              </>
            )}
          </ul>
        </nav>

        {/* Search Bar - Always Visible */}
        <div className="search-container" ref={searchRef}>
          <form onSubmit={handleSearchSubmit} className="search-form">
            <div className="search-input-wrapper">
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="search-input"
              />
              <button type="submit" className="search-submit-btn" disabled={searchLoading}>
                {searchLoading ? <FaSpinner className="search-spinner" /> : <FaSearch />}
              </button>
            </div>
          </form>

          {/* Search Results Dropdown */}
          {showSearchResults && (
            <div className="search-results">
              {searchResults.length > 0 ? (
                <>
                  {searchResults.map((item) => (
                    <div
                      key={`${item.type}-${item.id}`}
                      className="search-result-item"
                      onClick={() => handleSearchResultClick(item.id, item.type)}
                    >
                      <div className="search-result-image">
                        {item.type === 'pack' ? (
                          item.image ? (
                            <img src={item.image} alt={item.name} />
                          ) : (
                            <div className="search-result-placeholder pack-placeholder">
                              <FaGift />
                            </div>
                          )
                        ) : (
                          item.images && item.images.length > 0 ? (
                            <img src={item.images[0].image} alt={item.name} />
                          ) : (
                            <div className="search-result-placeholder">
                              <FaShoppingBag />
                            </div>
                          )
                        )}
                      </div>
                      <div className="search-result-info">
                        <h4>{item.name}</h4>
                        <p className="search-result-type">
                          {item.type === 'pack' ? '🎁 Pack' : '📦 Product'}
                        </p>
                        <p className="search-result-price">
                          {item.type === 'pack' ? item.pack_price : item.price} TND
                        </p>
                      </div>
                    </div>
                  ))}
                  <div className="search-result-footer">
                    <button onClick={handleSearchSubmit} className="view-all-results">
                      View all results
                    </button>
                  </div>
                </>
              ) : (
                <div className="no-search-results">
                  <p>No products found</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
