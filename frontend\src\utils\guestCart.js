// Guest cart localStorage helper functions
const GUEST_CART_KEY = 'nordica_guest_cart';

/**
 * Get guest cart from localStorage
 * @returns {Object} Cart object with items array and total
 */
export const getGuestCart = () => {
  try {
    const cartData = localStorage.getItem(GUEST_CART_KEY);
    if (!cartData) {
      return { items: [], total: 0 };
    }
    
    const cart = JSON.parse(cartData);
    
    // Validate cart structure
    if (!cart || typeof cart !== 'object' || !Array.isArray(cart.items)) {
      console.warn('Invalid guest cart data found, resetting to empty cart');
      return { items: [], total: 0 };
    }
    
    // Calculate total from items to ensure consistency
    const calculatedTotal = cart.items.reduce((sum, item) => {
      if (item.type === 'pack' && item.pack) {
        return sum + (parseFloat(item.pack.pack_price || 0) * parseInt(item.quantity || 0, 10));
      } else if (item.product) {
        return sum + (parseFloat(item.product.price || 0) * parseInt(item.quantity || 0, 10));
      }
      return sum;
    }, 0);
    
    return {
      ...cart,
      total: calculatedTotal
    };
  } catch (error) {
    console.error('Error reading guest cart from localStorage:', error);
    return { items: [], total: 0 };
  }
};

/**
 * Save guest cart to localStorage
 * @param {Object} cart - Cart object with items array
 */
export const setGuestCart = (cart) => {
  try {
    // Validate cart structure
    if (!cart || typeof cart !== 'object' || !Array.isArray(cart.items)) {
      console.error('Invalid cart data provided to setGuestCart');
      return false;
    }
    
    // Calculate total from items
    const total = cart.items.reduce((sum, item) => {
      if (item.type === 'pack' && item.pack) {
        return sum + (parseFloat(item.pack.pack_price || 0) * parseInt(item.quantity || 0, 10));
      } else if (item.product) {
        return sum + (parseFloat(item.product.price || 0) * parseInt(item.quantity || 0, 10));
      }
      return sum;
    }, 0);
    
    const cartToSave = {
      ...cart,
      total,
      lastUpdated: new Date().toISOString()
    };
    
    localStorage.setItem(GUEST_CART_KEY, JSON.stringify(cartToSave));
    return true;
  } catch (error) {
    console.error('Error saving guest cart to localStorage:', error);
    
    // Handle quota exceeded error
    if (error.name === 'QuotaExceededError') {
      console.warn('localStorage quota exceeded, attempting to clear old data');
      try {
        // Try to clear some space by removing old data
        clearGuestCart();
        localStorage.setItem(GUEST_CART_KEY, JSON.stringify(cart));
        return true;
      } catch (retryError) {
        console.error('Failed to save cart even after clearing storage:', retryError);
      }
    }
    
    return false;
  }
};

/**
 * Clear guest cart from localStorage
 */
export const clearGuestCart = () => {
  try {
    localStorage.removeItem(GUEST_CART_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing guest cart from localStorage:', error);
    return false;
  }
};

/**
 * Add item to guest cart
 * @param {Object} product - Product object
 * @param {number} quantity - Quantity to add
 * @returns {Object} Updated cart
 */
export const addToGuestCart = (product, quantity = 1) => {
  try {
    const cart = getGuestCart();
    const existingItemIndex = cart.items.findIndex(item => item.product.id === product.id);
    
    if (existingItemIndex >= 0) {
      // Update existing item quantity
      cart.items[existingItemIndex].quantity += parseInt(quantity, 10);
    } else {
      // Add new item
      cart.items.push({
        id: Date.now(),
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image,
          category_name: product.category_name,
          is_available: product.is_available,
          stock: product.stock
        },
        quantity: parseInt(quantity, 10),
        type: 'product'
      });
    }
    
    setGuestCart(cart);
    return cart;
  } catch (error) {
    console.error('Error adding item to guest cart:', error);
    throw error;
  }
};

/**
 * Update item quantity in guest cart
 * @param {number} productId - Product ID
 * @param {number} quantity - New quantity
 * @returns {Object} Updated cart
 */
export const updateGuestCartQuantity = (productId, quantity) => {
  try {
    const cart = getGuestCart();
    const itemIndex = cart.items.findIndex(item => item.product.id === productId);
    
    if (itemIndex >= 0) {
      const newQuantity = parseInt(quantity, 10);
      if (newQuantity <= 0) {
        // Remove item if quantity is 0 or negative
        cart.items.splice(itemIndex, 1);
      } else {
        cart.items[itemIndex].quantity = newQuantity;
      }
      
      setGuestCart(cart);
    }
    
    return cart;
  } catch (error) {
    console.error('Error updating guest cart quantity:', error);
    throw error;
  }
};

/**
 * Remove item from guest cart
 * @param {number} productId - Product ID to remove
 * @returns {Object} Updated cart
 */
export const removeFromGuestCart = (productId) => {
  try {
    const cart = getGuestCart();
    cart.items = cart.items.filter(item => item.product.id !== productId);
    
    setGuestCart(cart);
    return cart;
  } catch (error) {
    console.error('Error removing item from guest cart:', error);
    throw error;
  }
};



/**
 * Check if localStorage is available
 * @returns {boolean} True if localStorage is available
 */
export const isLocalStorageAvailable = () => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (error) {
    console.warn('localStorage is not available:', error);
    return false;
  }
};

/**
 * Get cart item count for guest cart
 * @returns {number} Total number of items in cart
 */
export const getGuestCartItemCount = () => {
  try {
    const cart = getGuestCart();
    return cart.items.reduce((count, item) => count + parseInt(item.quantity || 0, 10), 0);
  } catch (error) {
    console.error('Error getting guest cart item count:', error);
    return 0;
  }
};
