/* IMPROVED NAVIGATION BAR */

/* Header */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header.scrolled {
  box-shadow: var(--shadow);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding: var(--space-3) var(--space-4);
}

/* Desktop Header Layout */
@media (min-width: 769px) {
  .header-container {
    display: grid;
    grid-template-columns: auto var(--space-6) 1fr;
    grid-template-rows: auto auto;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    align-items: start;
  }
}

/* Logo - Left Aligned on Desktop */
.logo-centered {
  margin-bottom: var(--space-4);
}

.logo-centered .logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-centered .logo-img {
  height: 70px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo-centered .logo-img:hover {
  transform: scale(1.05);
}

/* Desktop Logo Positioning */
@media (min-width: 769px) {
  .logo-centered {
    margin-bottom: 0;
    margin-left: -var(--space-2);
    grid-column: 1;
    grid-row: 1 / 3;
    display: flex;
    align-items: center;
    z-index: 1;
  }

  .logo-centered .logo-img {
    height: 80px;
  }
}

/* Mobile Top Bar */
.mobile-top-bar {
  display: none;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: var(--space-3) 0;
  margin-bottom: var(--space-3);
}

.mobile-logo {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.mobile-logo .logo-img {
  height: 55px;
  width: auto;
  transition: transform 0.3s ease;
}

.mobile-logo .logo-img:hover {
  transform: scale(1.05);
}

.mobile-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  position: relative;
  z-index: 2;
}

.mobile-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--gray);
  text-decoration: none;
  border-radius: 50%;
  transition: all 0.2s ease;
  position: relative;
}

.mobile-action-btn:hover {
  color: var(--red);
  background: rgba(220, 38, 38, 0.1);
}

.cart-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--red);
  color: var(--white);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
}

/* Desktop Only Elements */
.desktop-only {
  display: block;
}

/* Navigation */
.nav {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-4);
}

/* Desktop Navigation */
@media (min-width: 769px) {
  .nav {
    width: auto;
    margin-bottom: 0;
    grid-column: 3;
    grid-row: 1;
    justify-content: flex-start;
    align-self: center;
  }
}

.nav ul {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  list-style: none;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
  justify-content: center;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  color: var(--gray);
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s;
  font-weight: 600;
  font-size: 0.9rem;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--red);
  background: var(--gray-light);
}

.nav-link svg {
  font-size: 1rem;
}

/* Cart Badge */
.icon-with-badge {
  position: relative;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--red);
  color: var(--white);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

/* Logout Button */
.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

/* Old mobile actions styles removed - using new mobile-top-bar design */

.mobile-menu-btn {
  background: var(--white);
  border: 2px solid var(--border);
  color: var(--gray);
  font-size: 1.4rem;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: 8px;
  transition: all 0.15s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  position: relative;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  outline: none;
}

.mobile-menu-btn:hover {
  color: var(--red);
  background: var(--white);
  border-color: var(--red);
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.2);
  transform: translateY(-1px);
}

.mobile-menu-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
  background: rgba(220, 38, 38, 0.05);
}

.mobile-menu-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.mobile-menu-btn:focus {
  outline: 2px solid var(--red);
  outline-offset: 2px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header-container {
    padding: var(--space-3) var(--space-4);
    flex-direction: column;
  }

  /* Show mobile top bar */
  .mobile-top-bar {
    display: flex;
  }

  /* Hide desktop logo */
  .desktop-only {
    display: none;
  }

  /* Mobile menu button */
  .mobile-menu-btn {
    background: none;
    border: none;
    color: var(--gray);
    font-size: 1.2rem;
    padding: var(--space-2);
    cursor: pointer;
    transition: color 0.2s ease;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
  }

  .mobile-menu-btn:hover {
    color: var(--red);
    background: rgba(220, 38, 38, 0.1);
  }

  .nav {
    margin-bottom: var(--space-3);
  }

  .nav ul {
    gap: var(--space-4);
  }

  .nav-link {
    font-size: 0.9rem;
    padding: var(--space-2) var(--space-3);
  }

  .logo-img {
    height: 40px;
  }

  .logo-text {
    font-size: 1.2rem;
  }

  .nav {
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100vh;
    background: var(--white);
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: var(--space-8) 0;
    transition: left 0.25s ease-out;
    z-index: 9999;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    will-change: transform;
  }

  .nav.nav-open {
    left: 0;
  }

  .nav.nav-open .nav-links li {
    animation: slideInFromLeft 0.4s ease-out forwards;
  }

  .nav.nav-open .nav-links li:nth-child(1) { animation-delay: 0.1s; }
  .nav.nav-open .nav-links li:nth-child(2) { animation-delay: 0.15s; }
  .nav.nav-open .nav-links li:nth-child(3) { animation-delay: 0.2s; }
  .nav.nav-open .nav-links li:nth-child(4) { animation-delay: 0.25s; }
  .nav.nav-open .nav-links li:nth-child(5) { animation-delay: 0.3s; }
  .nav.nav-open .nav-links li:nth-child(6) { animation-delay: 0.35s; }
  .nav.nav-open .nav-links li:nth-child(7) { animation-delay: 0.4s; }
  .nav.nav-open .nav-links li:nth-child(8) { animation-delay: 0.45s; }

  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .nav-links {
    flex-direction: column;
    width: 100%;
    padding: var(--space-4);
    margin: 0;
    gap: var(--space-1);
  }

  .nav-links li {
    width: 100%;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4) var(--space-5);
    color: var(--gray);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    width: 100%;
    justify-content: flex-start;
    border-radius: 8px;
    font-size: 1.1rem;
    border-left: 4px solid transparent;
  }

  .nav-link:hover,
  .nav-link.active {
    background: rgba(220, 38, 38, 0.1);
    color: var(--red);
    border-left-color: var(--red);
  }

  .nav-link svg {
    font-size: 1.2rem;
    color: var(--gray);
    transition: color 0.2s ease;
  }

  .nav-link:hover svg,
  .nav-link.active svg {
    color: var(--red);
  }

  .nav-text {
    font-size: 1.1rem;
    font-weight: 500;
  }

  .mobile-actions {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    gap: var(--space-2);
  }

  /* Mobile Search Styles */
  .search-container {
    width: 100%;
    margin-top: 0;
    order: 3;
  }

  .search-input {
    font-size: 0.9rem;
    padding: var(--space-2) var(--space-3);
    padding-right: 50px;
  }

  .search-submit-btn {
    width: 35px;
    height: 35px;
  }

  .mobile-search {
    display: block;
  }

  /* Mobile cart styling */
  .mobile-cart {
    padding: var(--space-2);
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .mobile-cart:hover {
    background: var(--gray-light);
  }

  /* Mobile menu button - Enhanced styling */
  .mobile-menu-btn {
    padding: var(--space-3);
    font-size: 1.4rem;
    background: var(--white);
    border: 2px solid var(--border);
    color: var(--gray);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.15s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    user-select: none;
    outline: none;
  }

  .mobile-menu-btn:hover {
    color: var(--red);
    background: var(--white);
    border-color: var(--red);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.2);
    transform: translateY(-1px);
  }

  .mobile-menu-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
  }

  .mobile-menu-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
  }

  .mobile-menu-btn:focus {
    outline: 2px solid var(--red);
    outline-offset: 2px;
  }

  /* Badge positioning for mobile */
  .icon-with-badge .badge {
    top: -8px;
    right: -8px;
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: var(--space-2) var(--space-3);
  }

  .logo-img {
    height: 35px;
  }

  .logo-text {
    font-size: 1rem;
  }

  .nav-link {
    padding: var(--space-3) var(--space-4);
    font-size: 1rem;
  }

  .nav-text {
    font-size: 1rem;
  }

  .mobile-actions {
    gap: var(--space-2);
  }

  /* Search responsive adjustments */
  .search-input {
    font-size: 0.9rem;
    padding: var(--space-2) var(--space-3);
    padding-right: 45px;
  }

  .search-button {
    width: 32px;
    height: 32px;
    right: 6px;
  }

  .search-result-item {
    padding: var(--space-2);
  }

  .search-result-image {
    width: 40px;
    height: 40px;
  }

  .search-result-info h4 {
    font-size: 0.8rem;
  }

  .search-result-price {
    font-size: 0.75rem;
  }

  .mobile-cart {
    font-size: 1.1rem;
    padding: var(--space-2);
  }

  .mobile-menu-btn {
    font-size: 1.2rem !important;
    padding: var(--space-2) !important;
    width: 44px !important;
    height: 44px !important;
    border-radius: 10px !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: var(--white) !important;
    border: 2px solid var(--border) !important;
    color: var(--gray) !important;
  }

  .nav-link {
    padding: var(--space-3) var(--space-4);
    font-size: 1rem;
  }

  .nav-link svg {
    font-size: 1.2rem;
  }
}

/* SEARCH COMPONENT STYLES */
.search-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

/* Desktop Search */
@media (min-width: 769px) {
  .search-container {
    width: 400px;
    max-width: 400px;
    margin: 0;
    grid-column: 3;
    grid-row: 2;
    justify-self: center;
    align-self: center;
  }
}

.search-form {
  position: relative;
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  padding-right: 60px;
  border: 2px solid var(--border);
  border-radius: 25px;
  font-size: 1rem;
  background: var(--white);
  color: var(--gray);
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: var(--red);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.search-input::placeholder {
  color: var(--gray-light);
}

.search-submit-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--red);
  border: none;
  color: var(--white);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-submit-btn:hover {
  background: var(--red-dark);
  transform: translateY(-50%) scale(1.05);
}

.search-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.search-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Search Results Dropdown */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border: 2px solid var(--border);
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border);
}

.search-result-item:hover {
  background: var(--gray-light);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-image {
  width: 50px;
  height: 50px;
  margin-right: var(--space-3);
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.search-result-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.search-result-placeholder {
  width: 100%;
  height: 100%;
  background: var(--gray-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray);
}

.search-result-placeholder.pack-placeholder {
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
}

.search-result-info {
  flex: 1;
}

.search-result-info h4 {
  margin: 0 0 var(--space-1) 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gray);
  line-height: 1.3;
}

.search-result-type {
  font-size: 0.75rem;
  color: var(--gray-light);
  margin-bottom: var(--space-1);
  font-weight: 500;
}

.search-result-price {
  margin: 0;
  font-size: 0.8rem;
  color: var(--red);
  font-weight: 600;
}

.search-result-footer {
  padding: var(--space-3);
  border-top: 1px solid var(--border);
  text-align: center;
}

.view-all-results {
  background: var(--red);
  color: var(--white);
  border: none;
  padding: var(--space-2) var(--space-4);
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.view-all-results:hover {
  background: var(--red-dark);
}

.no-search-results {
  padding: var(--space-4);
  text-align: center;
  color: var(--gray);
}

.no-search-results p {
  margin: 0;
  font-size: 0.9rem;
}

