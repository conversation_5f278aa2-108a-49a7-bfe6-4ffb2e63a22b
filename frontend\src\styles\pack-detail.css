/* PACK DETAIL PAGE */
.pack-detail-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
  background: var(--gray-light);
  min-height: 100vh;
}

/* Header */
.pack-detail-header {
  margin-bottom: var(--space-6);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--gray);
  text-decoration: none;
  font-weight: 600;
  padding: var(--space-2) var(--space-4);
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid var(--border);
  background: var(--white);
}

.back-button:hover {
  color: var(--red);
  border-color: var(--red);
  background: rgba(220, 38, 38, 0.05);
}

/* Pack Detail Container */
.pack-detail-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  background: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

/* Pack Image Section */
.pack-image-section {
  position: relative;
}

.pack-image-container {
  position: relative;
  height: 400px;
  overflow: hidden;
  background: var(--gray-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pack-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pack-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

/* Discount Badge */
.pack-discount-badge {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: var(--white);
  padding: var(--space-3) var(--space-5);
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: 0 4px 16px rgba(255, 71, 87, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Pack Info Section */
.pack-info-section {
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.pack-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--black);
  margin-bottom: var(--space-3);
  line-height: 1.2;
}

.pack-header p {
  font-size: 1.2rem;
  color: var(--gray);
  line-height: 1.6;
  margin: 0;
}

/* Pricing Detail */
.pack-pricing-detail {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: var(--space-5);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.pack-prices {
  display: flex;
  align-items: baseline;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
}

.pack-original-price {
  font-size: 1.3rem;
  color: var(--gray-light);
  text-decoration: line-through;
  font-weight: 500;
}

.pack-current-price {
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pack-savings {
  font-size: 1.1rem;
  color: #28a745;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Pack Products Detail */
.pack-products-detail h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pack-products-detail h3::before {
  content: '📦';
  font-size: 1.2rem;
}

.pack-products-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.pack-product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: rgba(220, 38, 38, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(220, 38, 38, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.pack-product-item:hover {
  background: rgba(220, 38, 38, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
  border-color: rgba(220, 38, 38, 0.2);
}

.pack-product-item.clickable {
  cursor: pointer;
}

.pack-product-item.clickable:hover .product-details h4 {
  color: var(--red);
}

.pack-product-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.check-icon {
  color: var(--red);
  font-size: 1.2rem;
}

.product-details h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: var(--space-1);
}

.product-category {
  font-size: 0.9rem;
  color: var(--gray);
  margin-bottom: var(--space-1);
}

.product-price {
  font-size: 1rem;
  color: var(--red);
  font-weight: 600;
}

.quantity-badge {
  background: var(--red);
  color: var(--white);
  padding: var(--space-2) var(--space-3);
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
}

/* Pack Actions */
.pack-add-to-cart-btn-detail {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border: none;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.pack-add-to-cart-btn-detail:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--red-dark) 0%, #c53030 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.pack-add-to-cart-btn-detail:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Pack Benefits */
.pack-benefits h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: var(--space-4);
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.benefits-list li {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 1rem;
  color: var(--gray);
  padding: var(--space-2) 0;
}

.benefits-list li svg {
  color: #28a745;
  font-size: 1.1rem;
}

/* Loading and Error States */
.loading-container,
.error-container {
  text-align: center;
  padding: var(--space-12);
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  max-width: 600px;
  margin: 0 auto;
}

.loading-spinner {
  font-size: 2rem;
  color: var(--red);
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .pack-detail-container {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .pack-image-container {
    height: 300px;
  }

  .pack-info-section {
    padding: var(--space-6);
  }

  .pack-header h1 {
    font-size: 2rem;
  }

  .pack-current-price {
    font-size: 1.8rem;
  }

  .pack-product-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .quantity-badge {
    align-self: flex-end;
  }
}
