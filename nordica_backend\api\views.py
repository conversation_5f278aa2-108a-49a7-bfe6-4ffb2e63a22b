from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token
from .models import *
from .serializers import *


class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [AllowAny]


class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.filter(is_available=True)
    serializer_class = ProductSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Product.objects.filter(is_available=True)

        # Filter by category
        category = self.request.query_params.get('category', None)
        if category is not None:
            queryset = queryset.filter(category_id=category)

        # Filter by featured status
        is_featured = self.request.query_params.get('is_featured', None)
        if is_featured is not None:
            queryset = queryset.filter(is_featured=True)

        # Filter by search query
        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        # Filter by availability
        is_available = self.request.query_params.get('is_available', None)
        if is_available is not None:
            if is_available.lower() == 'true':
                queryset = queryset.filter(stock__gt=0)
            else:
                queryset = queryset.filter(stock=0)

        # Filter by price range
        min_price = self.request.query_params.get('min_price', None)
        if min_price is not None:
            queryset = queryset.filter(price__gte=min_price)

        max_price = self.request.query_params.get('max_price', None)
        if max_price is not None:
            queryset = queryset.filter(price__lte=max_price)

        # Ordering
        ordering = self.request.query_params.get('ordering', None)
        if ordering is not None:
            queryset = queryset.order_by(ordering)

        return queryset


class PackViewSet(viewsets.ModelViewSet):
    queryset = Pack.objects.filter(is_active=True)
    serializer_class = PackSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Pack.objects.filter(is_active=True)

        # Filter by featured status
        is_featured = self.request.query_params.get('is_featured', None)
        if is_featured is not None:
            queryset = queryset.filter(is_featured=True)

        # Filter by search query
        search = self.request.query_params.get('search', None)
        if search is not None:
            queryset = queryset.filter(name__icontains=search)

        # Ordering
        ordering = self.request.query_params.get('ordering', None)
        if ordering is not None:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by('-created_at')

        return queryset


class SiteSettingsView(APIView):
    """
    Get site settings
    """
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            settings = SiteSettings.get_settings()
            serializer = SiteSettingsSerializer(settings)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': 'Failed to fetch site settings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CartViewSet(viewsets.ModelViewSet):
    serializer_class = CartSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Cart.objects.filter(user=self.request.user)

    def list(self, request):
        """Get user's cart - return single cart object instead of paginated list"""
        try:
            cart, created = Cart.objects.get_or_create(user=request.user)
            serializer = self.get_serializer(cart)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def add_item(self, request):
        """Add item to cart"""
        try:
            product_id = request.data.get('product_id')
            pack_id = request.data.get('pack_id')
            quantity = request.data.get('quantity', 1)

            if not product_id and not pack_id:
                return Response({'error': 'Either product_id or pack_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            if product_id and pack_id:
                return Response({'error': 'Cannot specify both product_id and pack_id'}, status=status.HTTP_400_BAD_REQUEST)

            # Get or create cart for user
            cart, created = Cart.objects.get_or_create(user=request.user)

            if product_id:
                try:
                    product = Product.objects.get(id=product_id)
                except Product.DoesNotExist:
                    return Response({'error': 'Product not found'}, status=status.HTTP_404_NOT_FOUND)

                # Check if item already exists in cart
                cart_item, item_created = CartItem.objects.get_or_create(
                    cart=cart,
                    product=product,
                    defaults={'quantity': quantity}
                )

                if not item_created:
                    # Item exists, update quantity
                    cart_item.quantity += int(quantity)
                    cart_item.save()

            elif pack_id:
                try:
                    pack = Pack.objects.get(id=pack_id)
                except Pack.DoesNotExist:
                    return Response({'error': 'Pack not found'}, status=status.HTTP_404_NOT_FOUND)

                # Check if pack already exists in cart
                cart_item, item_created = CartItem.objects.get_or_create(
                    cart=cart,
                    pack=pack,
                    defaults={'quantity': quantity}
                )

                if not item_created:
                    # Pack exists, update quantity
                    cart_item.quantity += int(quantity)
                    cart_item.save()

            # Return updated cart
            serializer = self.get_serializer(cart)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def update_quantity(self, request):
        """Update item quantity in cart"""
        try:
            product_id = request.data.get('product_id')
            pack_id = request.data.get('pack_id')
            quantity = request.data.get('quantity')

            if (not product_id and not pack_id) or quantity is None:
                return Response({'error': 'Either product_id or pack_id, and quantity are required'}, status=status.HTTP_400_BAD_REQUEST)

            if product_id and pack_id:
                return Response({'error': 'Cannot specify both product_id and pack_id'}, status=status.HTTP_400_BAD_REQUEST)

            cart = Cart.objects.get(user=request.user)

            if product_id:
                cart_item = CartItem.objects.get(cart=cart, product_id=product_id)
            else:
                cart_item = CartItem.objects.get(cart=cart, pack_id=pack_id)

            if int(quantity) <= 0:
                cart_item.delete()
            else:
                cart_item.quantity = int(quantity)
                cart_item.save()

            # Return updated cart
            serializer = self.get_serializer(cart)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Cart.DoesNotExist:
            return Response({'error': 'Cart not found'}, status=status.HTTP_404_NOT_FOUND)
        except CartItem.DoesNotExist:
            return Response({'error': 'Cart item not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def remove_item(self, request):
        """Remove item from cart"""
        try:
            product_id = request.data.get('product_id')
            pack_id = request.data.get('pack_id')

            if not product_id and not pack_id:
                return Response({'error': 'Either product_id or pack_id is required'}, status=status.HTTP_400_BAD_REQUEST)

            if product_id and pack_id:
                return Response({'error': 'Cannot specify both product_id and pack_id'}, status=status.HTTP_400_BAD_REQUEST)

            cart = Cart.objects.get(user=request.user)

            # Debug: Print current cart items
            print(f"🛒 Current cart items for user {request.user.id}:")
            for item in cart.items.all():
                if item.product:
                    print(f"  - Product ID: {item.product.id}, Name: {item.product.name}")
                elif item.pack:
                    print(f"  - Pack ID: {item.pack.id}, Name: {item.pack.name}")

            print(f"🗑️ Trying to remove: product_id={product_id}, pack_id={pack_id}")

            if product_id:
                cart_item = CartItem.objects.get(cart=cart, product_id=product_id)
            else:
                cart_item = CartItem.objects.get(cart=cart, pack_id=pack_id)

            cart_item.delete()

            # Return updated cart
            serializer = self.get_serializer(cart)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Cart.DoesNotExist:
            return Response({'error': 'Cart not found'}, status=status.HTTP_404_NOT_FOUND)
        except CartItem.DoesNotExist:
            return Response({'error': 'Cart item not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def clear(self, request):
        """Clear all items from cart"""
        try:
            cart = Cart.objects.get(user=request.user)
            CartItem.objects.filter(cart=cart).delete()

            # Return updated cart
            serializer = self.get_serializer(cart)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Cart.DoesNotExist:
            return Response({'error': 'Cart not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OrderViewSet(viewsets.ModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [AllowAny]  # Allow guest orders

    def get_queryset(self):
        # If user is authenticated, return their orders with items prefetched
        if self.request.user.is_authenticated:
            return Order.objects.filter(user=self.request.user).prefetch_related('items__product')
        # For guest users, return empty queryset (they can only create orders)
        return Order.objects.none()

    def perform_create(self, serializer):
        # If user is authenticated, associate order with user
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user)
        else:
            # For guest orders, create a temporary guest user or use a default guest user
            # We'll use the authenticated user if available, otherwise create with a guest user
            try:
                # Try to get or create a guest user
                guest_user, created = User.objects.get_or_create(
                    username='guest_user',
                    defaults={
                        'email': '<EMAIL>',
                        'first_name': 'Guest',
                        'last_name': 'User',
                        'is_active': False  # Mark as inactive so it can't be used for login
                    }
                )
                serializer.save(user=guest_user)
            except Exception as e:
                # If there's any issue, just use the first superuser as fallback
                admin_user = User.objects.filter(is_superuser=True).first()
                if admin_user:
                    serializer.save(user=admin_user)
                else:
                    # This should not happen in a properly set up system
                    raise Exception("No user available for guest order")





@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    username = request.data.get('username')
    password = request.data.get('password')
    
    if username and password:
        user = authenticate(username=username, password=password)
        if user:
            token, created = Token.objects.get_or_create(user=user)
            return Response({
                'token': token.key,
                'user': UserSerializer(user).data
            })
    
    return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)


@api_view(['POST'])
@permission_classes([AllowAny])
def register_view(request):
    username = request.data.get('username')
    email = request.data.get('email')
    password = request.data.get('password')
    
    if username and email and password:
        if User.objects.filter(username=username).exists():
            return Response({'error': 'Username already exists'}, status=status.HTTP_400_BAD_REQUEST)
        
        user = User.objects.create_user(username=username, email=email, password=password)
        token, created = Token.objects.get_or_create(user=user)
        
        return Response({
            'token': token.key,
            'user': UserSerializer(user).data
        })
    
    return Response({'error': 'Missing required fields'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    try:
        # Delete the user's token to logout
        request.user.auth_token.delete()
        return Response({'message': 'Successfully logged out'})
    except:
        return Response({'message': 'Successfully logged out'})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def profile_view(request):
    return Response({
        'token': request.user.auth_token.key,
        'user': UserSerializer(request.user).data
    })


@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def profile_update_view(request):
    if request.method == 'GET':
        # Get user profile
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = UserProfileSerializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            # Create profile if it doesn't exist
            profile = UserProfile.objects.create(user=request.user)
            serializer = UserProfileSerializer(profile)
            return Response(serializer.data)

    elif request.method == 'PUT':
        # Update user profile
        try:
            profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=request.user)

        # Update user basic info
        user_data = request.data.get('user', {})
        if user_data:
            user = request.user
            user.first_name = user_data.get('first_name', user.first_name)
            user.last_name = user_data.get('last_name', user.last_name)
            user.email = user_data.get('email', user.email)
            user.save()

        # Update profile info
        profile.phone_number = request.data.get('phone_number', profile.phone_number)
        profile.address = request.data.get('address', profile.address)
        profile.city = request.data.get('city', profile.city)
        profile.state = request.data.get('state', profile.state)
        profile.zip_code = request.data.get('zip_code', profile.zip_code)
        profile.save()

        # Return updated data
        return Response({
            'user': UserSerializer(request.user).data,
            'profile': UserProfileSerializer(profile).data
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password_view(request):
    old_password = request.data.get('old_password')
    new_password = request.data.get('new_password')

    if not old_password or not new_password:
        return Response({'error': 'Both old and new passwords are required'}, status=status.HTTP_400_BAD_REQUEST)

    user = request.user
    if not user.check_password(old_password):
        return Response({'error': 'Current password is incorrect'}, status=status.HTTP_400_BAD_REQUEST)

    user.set_password(new_password)
    user.save()

    return Response({'message': 'Password updated successfully'})



