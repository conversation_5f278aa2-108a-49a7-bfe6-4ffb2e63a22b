import { useState, useEffect } from 'react';
import { packsAPI } from '../services/api';
import PackCard from '../components/packs/PackCard';
import { FaSpinner, FaExclamationTriangle, FaGift } from 'react-icons/fa';
import '../styles/packs.css';

const Packs = () => {
  const [packs, setPacks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPacks = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await packsAPI.getAll();
        setPacks(response.data.results || response.data || []);
      } catch (err) {
        setError('Failed to load packs');
        console.error('Error fetching packs:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPacks();
  }, []);

  if (loading) {
    return (
      <div className="packs-page">
        <div className="loading-container">
          <FaSpinner className="loading-spinner" />
          <h2>Loading Packs...</h2>
          <p>Please wait while we fetch our amazing pack deals</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="packs-page">
        <div className="error-message">
          <FaExclamationTriangle size={40} />
          <h3>Error Loading Packs</h3>
          <p>{error}</p>
          <button onClick={() => window.location.reload()} className="btn btn-primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="packs-page">
      {/* Page Header */}
      <div className="packs-header">
        <h1>Product Packs</h1>
        <p>Save more with our specially curated product bundles</p>
      </div>

      {/* Packs Grid */}
      {packs.length > 0 ? (
        <div className="packs-container">
          <div className="packs-grid">
            {packs.map((pack) => (
              <PackCard key={pack.id} pack={pack} />
            ))}
          </div>
        </div>
      ) : (
        <div className="no-packs">
          <FaGift size={60} />
          <h3>No Packs Available</h3>
          <p>We're working on creating amazing pack deals for you. Check back soon!</p>
        </div>
      )}
    </div>
  );
};

export default Packs;
