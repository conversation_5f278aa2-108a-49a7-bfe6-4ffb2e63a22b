import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { useSiteSettings } from '../contexts/SiteSettingsContext';
import { categoriesAPI } from '../services/api';
import { FaMinus, FaPlus, FaTrashAlt, FaTruck, FaMoneyBillWave, FaShoppingCart } from 'react-icons/fa';
import '../styles/cart.css';

const Cart = () => {
  const { cart, updateQuantity, updatePackQuantity, removeFromCart, removePackFromCart, clearCart, loading } = useCart();
  const { isAuthenticated } = useAuth();
  const { settings, getDeliveryFee, getTaxAmount } = useSiteSettings();
  const navigate = useNavigate();

  // State for categories
  const [categories, setCategories] = useState([]);

  const [quantities, setQuantities] = useState({});

  // Fetch categories for empty cart suggestions
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await categoriesAPI.getAll();
        const categoriesData = response.data.results || response.data || [];
        // Get first 4 categories for display
        setCategories(categoriesData.slice(0, 4));
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Function to get category icon based on category name
  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase();
    if (name.includes('electronic') || name.includes('tech') || name.includes('phone') || name.includes('computer')) {
      return '📱';
    } else if (name.includes('fashion') || name.includes('clothing') || name.includes('apparel') || name.includes('wear')) {
      return '👕';
    } else if (name.includes('home') || name.includes('house') || name.includes('garden') || name.includes('furniture')) {
      return '🏠';
    } else if (name.includes('sport') || name.includes('fitness') || name.includes('gym') || name.includes('outdoor')) {
      return '⚽';
    } else if (name.includes('beauty') || name.includes('cosmetic') || name.includes('skincare')) {
      return '💄';
    } else if (name.includes('book') || name.includes('education') || name.includes('learning')) {
      return '📚';
    } else if (name.includes('toy') || name.includes('game') || name.includes('kid') || name.includes('child')) {
      return '🧸';
    } else if (name.includes('food') || name.includes('grocery') || name.includes('kitchen')) {
      return '🍎';
    } else if (name.includes('car') || name.includes('auto') || name.includes('vehicle')) {
      return '🚗';
    } else if (name.includes('health') || name.includes('medical') || name.includes('pharmacy')) {
      return '💊';
    } else {
      return '🛍️'; // Default shopping icon
    }
  };

  // Store the debounce timeout ID
  const debounceRef = React.useRef(null);

  // Cleanup debounce timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  // Handle quantity change and update cart automatically
  const handleQuantityChange = (e, productId, newQuantity) => {
    // Prevent default form submission behavior if event is provided
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!productId) {
      console.error('Invalid product ID');
      return;
    }

    // Ensure newQuantity is a valid number
    const validQuantity = parseInt(newQuantity);
    if (isNaN(validQuantity) || validQuantity <= 0) {
      console.error('Invalid quantity:', newQuantity);
      return;
    }

    // Find the cart item to check stock limits
    const cartItem = cart.items?.find(item => {
      if (!item) return false;
      const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);
      if (isPackItem && item.pack && item.pack.id) {
        return `pack_${item.pack.id}` === productId;
      } else if (item.product && item.product.id) {
        return item.product.id === productId;
      }
      return false;
    });

    if (!cartItem) {
      console.error('Cart item not found');
      return;
    }

    // Check stock limits - handle both guest cart format and server format
    const isPackItem = (cartItem.type === 'pack' && cartItem.pack) || (cartItem.pack && cartItem.product === null);
    const maxStock = isPackItem ? 99 : (cartItem.product?.stock || 99);
    const finalQuantity = Math.min(Math.max(1, validQuantity), maxStock);

    // Only update if quantity actually changed
    const currentQuantity = quantities[productId] || cartItem.quantity;
    if (currentQuantity === finalQuantity) {
      return;
    }

    // Update local state immediately for a responsive UI
    setQuantities(prev => ({ ...prev, [productId]: finalQuantity }));

    // Clear previous timeout if it exists
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Debounce the API call to prevent too many requests
    debounceRef.current = setTimeout(async () => {
      try {
        const isPackItem = (cartItem.type === 'pack' && cartItem.pack) || (cartItem.pack && cartItem.product === null);

        if (isPackItem && cartItem.pack && cartItem.pack.id) {
          // For pack items, update via server API
          await updatePackQuantity(cartItem.pack.id, finalQuantity);
          console.log(`Updated pack quantity for ${cartItem.pack.name || 'Unknown Pack'} to ${finalQuantity}`);
        } else if (cartItem.product && cartItem.product.id) {
          // For regular products, update via API
          await updateQuantity(cartItem.product.id, finalQuantity);
          console.log(`Updated quantity for product ${cartItem.product.id} to ${finalQuantity}`);
        } else {
          console.error('Invalid cart item structure:', cartItem);
        }

        // Clear the local quantity override after successful update
        setQuantities(prev => {
          const newQuantities = { ...prev };
          delete newQuantities[productId];
          return newQuantities;
        });
      } catch (err) {
        console.error('Error updating quantity:', err);
        // Revert local state on error
        setQuantities(prev => ({ ...prev, [productId]: cartItem.quantity }));
      }
    }, 500); // Increased debounce to 500ms to reduce API calls
  };

  // Helper function to increment quantity
  const handleIncrement = (itemId) => {
    const cartItem = cart.items?.find(item => {
      if (!item) return false;
      const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);
      if (isPackItem && item.pack && item.pack.id) {
        return `pack_${item.pack.id}` === itemId;
      } else if (item.product && item.product.id) {
        return item.product.id === itemId;
      }
      return false;
    });

    if (!cartItem) return;

    const currentQuantity = quantities[itemId] || cartItem.quantity;
    const isPackItem = (cartItem.type === 'pack' && cartItem.pack) || (cartItem.pack && cartItem.product === null);
    const maxStock = isPackItem ? 99 : (cartItem.product?.stock || 99);

    if (currentQuantity < maxStock) {
      handleQuantityChange(null, itemId, currentQuantity + 1);
    }
  };

  // Helper function to decrement quantity
  const handleDecrement = (itemId) => {
    const cartItem = cart.items?.find(item => {
      if (!item) return false;
      const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);
      if (isPackItem && item.pack && item.pack.id) {
        return `pack_${item.pack.id}` === itemId;
      } else if (item.product && item.product.id) {
        return item.product.id === itemId;
      }
      return false;
    });

    if (!cartItem) return;

    const currentQuantity = quantities[itemId] || cartItem.quantity;

    if (currentQuantity > 1) {
      handleQuantityChange(null, itemId, currentQuantity - 1);
    }
  };



  // Clear cart
  const handleClearCart = async () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      try {
        await clearCart();
      } catch (err) {
        console.error('Error clearing cart:', err);
      }
    }
  };

  // Ensure cart and cart.items exist to prevent white screen
  if (!cart) {
    console.error('Cart context not available');
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading cart data...</p>
      </div>
    );
  }

  const items = (cart?.items || []).filter(item => {
    // Filter out null or invalid items
    if (!item) return false;

    // Check if it's a valid pack item (either has type='pack' or has pack but product is null)
    const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);
    if (isPackItem) {
      return item.pack && item.pack.id;
    }

    // Check if it's a valid product item
    return item.product && item.product.id;
  });

  // Calculate subtotal
  const calculateSubtotal = () => {
    return items.reduce((sum, item) => {
      // Add null checks
      if (!item) return sum;

      const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);
      if (isPackItem && item.pack && item.pack.id) {
        // Handle pack items
        const packId = item.pack.id;
        const price = parseFloat(item.pack.pack_price || 0);
        const quantity = quantities[`pack_${packId}`] || item.quantity || 0;
        return sum + (price * quantity);
      } else if (item.product && item.product.id) {
        // Handle regular product items
        const productId = item.product.id;
        const price = parseFloat(item.product.price || 0);
        const quantity = quantities[productId] || item.quantity || 0;
        return sum + (price * quantity);
      }
      return sum;
    }, 0);
  };

  // Calculate total
  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const tax = getTaxAmount(subtotal);
    const deliveryFee = getDeliveryFee(subtotal);
    return subtotal + tax + deliveryFee;
  };

  // Proceed to checkout - requires authentication
  const handleCheckout = () => {
    if (!isAuthenticated) {
      // Redirect to login page if not authenticated
      navigate('/login', {
        state: {
          from: '/checkout',
          message: 'Please log in to proceed with checkout'
        }
      });
      return;
    }
    navigate('/checkout');
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading cart...</p>
      </div>
    );
  }

  return (
    <div className="cart-page" onClick={(e) => e.stopPropagation()}>
      <div className="cart-header">
        <h1>Your Shopping Cart</h1>
        <p>Review your items and proceed to checkout when ready</p>

        {!isAuthenticated && items.length > 0 && (
          <div className="guest-cart-notice">
            <p>
              <strong>Shopping as a guest?</strong>
              <Link to="/login" className="login-link"> Sign in</Link> to save your cart across devices and access order history.
            </p>
          </div>
        )}
      </div>

      {items.length === 0 ? (
        <div className="empty-cart">
          <div className="empty-cart-content">
            <div className="empty-cart-illustration">
              <div className="empty-cart-icon">
                <FaShoppingCart />
              </div>
            </div>

            <div className="empty-cart-text">
              <h2>Your cart is waiting for something amazing!</h2>
              <p>Start your shopping journey and discover our curated collection of premium products and exclusive packs.</p>
            </div>

            <div className="empty-cart-actions">
              <Link to="/products" className="btn btn-primary">
                <FaShoppingCart />
                <span>Browse Products</span>
              </Link>
              <Link to="/packs" className="btn btn-secondary">
                <span>🎁</span>
                <span>Explore Packs</span>
              </Link>
            </div>

            <div className="empty-cart-features">
              <div className="feature">
                <div className="feature-icon">
                  <span>🚚</span>
                </div>
                <div className="feature-content">
                  <h4>Free Delivery</h4>
                  <p>On orders over 300 TND</p>
                </div>
              </div>
              <div className="feature">
                <div className="feature-icon">
                  <span>💰</span>
                </div>
                <div className="feature-content">
                  <h4>Cash on Delivery</h4>
                  <p>Pay when you receive</p>
                </div>
              </div>
              <div className="feature">
                <div className="feature-icon">
                  <span>🔒</span>
                </div>
                <div className="feature-content">
                  <h4>Secure Shopping</h4>
                  <p>Your data is protected</p>
                </div>
              </div>
            </div>

            <div className="empty-cart-suggestions">
              <h3>Popular Categories</h3>
              <div className="category-links">
                <Link to="/products" className="category-link">
                  <span>�</span>
                  <span>All Products</span>
                </Link>
                {categories.length > 0 ? (
                  categories.map((category) => (
                    <Link
                      key={category.id}
                      to={`/products/category/${category.id}`}
                      className="category-link"
                    >
                      <span>{getCategoryIcon(category.name)}</span>
                      <span>{category.name}</span>
                    </Link>
                  ))
                ) : (
                  // Fallback categories if API fails
                  <>
                    <Link to="/products" className="category-link">
                      <span>🛍️</span>
                      <span>All Products</span>
                    </Link>
                    <Link to="/packs" className="category-link">
                      <span>🎁</span>
                      <span>Product Packs</span>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="cart-container">
          <div className="cart-items">
            <div className="cart-items-header">
              <div>Product</div>
              <div>Price</div>
              <div>Quantity</div>
              <div>Subtotal</div>
              <div>Action</div>
            </div>

            {items.map(item => {
              // Add null checks to prevent errors
              if (!item) return null;

              // Determine if it's a pack item based on the presence of pack data
              // Server response: pack items have pack object and product is null
              // Guest cart: pack items have type='pack' and pack object
              const isPackItem = (item.type === 'pack' && item.pack) || (item.pack && item.product === null);
              const hasValidProduct = item.product && item.product.id;
              const hasValidPack = item.pack && item.pack.id;

              // Skip items that don't have valid product or pack data
              if (!isPackItem && !hasValidProduct) {
                console.warn('Skipping cart item with invalid product data:', item);
                return null;
              }

              if (isPackItem && !hasValidPack) {
                console.warn('Skipping cart item with invalid pack data:', item);
                return null;
              }

              const itemId = isPackItem ? `pack_${item.pack.id}` : item.product.id;
              const itemKey = isPackItem ? `pack_${item.pack.id}` : item.product.id;

              return (
                <div key={itemKey} className="cart-item cart-item-product">
                  {/* 1. Product Info */}
                  <div className="cart-item-product">
                    <div className="cart-item-image">
                      {isPackItem ? (
                        // Pack image - treat like product
                        item.pack.image ? (
                          <img
                            src={item.pack.image}
                            alt={item.pack.name}
                          />
                        ) : (
                          <div className="cart-item-image-placeholder">
                            📦
                          </div>
                        )
                      ) : (
                        // Product image
                        item.product.images && item.product.images.length > 0 ? (
                          <img
                            src={item.product.images[0].image}
                            alt={item.product.name}
                          />
                        ) : (
                          <div className="cart-item-image-placeholder">
                            📦
                          </div>
                        )
                      )}
                    </div>
                    <div className="cart-item-details">
                      <h3 className="cart-item-name">
                        {isPackItem ? (
                          <Link to={`/packs/${item.pack.id}`}>
                            {item.pack.name}
                          </Link>
                        ) : (
                          <Link to={`/products/${item.product.id}`}>
                            {item.product.name}
                          </Link>
                        )}
                      </h3>
                      <p className="cart-item-category">
                        {isPackItem ? item.pack.category_name || 'Pack' : item.product.category_name}
                      </p>
                    </div>
                  </div>

                  {/* 2. Price Display */}
                  <div className="cart-item-price-display">
                    <div className="cart-item-price">
                      {isPackItem
                        ? `${parseFloat(item.pack.pack_price || 0).toFixed(2)} TND`
                        : `${parseFloat(item.product.price || 0).toFixed(2)} TND`
                      }
                    </div>
                  </div>

                  {/* 3. Quantity Selector */}
                  <div className="cart-item-quantity-section">
                    <form
                      onSubmit={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                      }}
                      className="quantity-selector"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <button
                        type="button"
                        className="quantity-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleDecrement(itemId);
                        }}
                        disabled={(quantities[itemId] || item.quantity) <= 1}
                        aria-label="Decrease quantity"
                      >
                        <FaMinus />
                      </button>
                      <input
                        type="number"
                        value={quantities[itemId] || item.quantity}
                        onChange={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const value = parseInt(e.target.value) || 1;
                          setQuantities(prev => ({ ...prev, [itemId]: value }));
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            e.stopPropagation();
                            e.target.blur();
                          }
                        }}
                        onBlur={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const value = parseInt(e.target.value) || 1;
                          handleQuantityChange(null, itemId, value);
                        }}
                        min="1"
                        max={isPackItem ? 99 : (item.product?.stock || 99)}
                        className="quantity-input"
                        aria-label="Quantity"
                      />
                      <button
                        type="button"
                        className="quantity-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleIncrement(itemId);
                        }}
                        disabled={!isPackItem && (quantities[itemId] || item.quantity) >= (item.product?.stock || 99)}
                        aria-label="Increase quantity"
                      >
                        <FaPlus />
                      </button>
                    </form>
                  </div>

                  {/* 4. Subtotal Display */}
                  <div className="cart-item-subtotal-display">
                    <div className="cart-item-subtotal">
                      {(() => {
                        const price = isPackItem
                          ? parseFloat(item.pack.pack_price || 0)
                          : parseFloat(item.product.price || 0);
                        const quantity = quantities[itemId] || item.quantity;
                        const subtotal = price * quantity;
                        return `${subtotal.toFixed(2)} TND`;
                      })()}
                    </div>
                  </div>

                  {/* 5. Remove Button */}
                  <div className="cart-item-actions">
                    <button
                      type="button"
                      className="remove-btn"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (isPackItem) {
                          removePackFromCart(item.pack.id);
                        } else {
                          removeFromCart(item.product.id);
                        }
                      }}
                      aria-label={`Remove ${isPackItem ? item.pack.name : item.product.name} from cart`}
                    >
                      <FaTrashAlt />
                    </button>
                  </div>
                </div>
              );
            })}


          </div>

          <div className="cart-summary">
            <h2>Order Summary</h2>

            <div className="cart-summary-row">
              <span>Subtotal:</span>
              <span>{parseFloat(calculateSubtotal()).toFixed(2)} DT</span>
            </div>

            <div className="cart-summary-row">
              <span>Tax ({settings?.tax_percentage ? parseFloat(settings.tax_percentage) : 0}%):</span>
              <span>{parseFloat(getTaxAmount(calculateSubtotal())).toFixed(2)} TND</span>
            </div>

            <div className="cart-summary-row">
              <span>Delivery:</span>
              {parseFloat(getDeliveryFee(calculateSubtotal())) > 0 ? (
                <span>{parseFloat(getDeliveryFee(calculateSubtotal())).toFixed(2)} TND</span>
              ) : (
                <span className="free-delivery">Free</span>
              )}
            </div>

            <div className="cart-summary-row total">
              <span>Total:</span>
              <span>{parseFloat(calculateTotal()).toFixed(2)} DT</span>
            </div>

            <div className="cart-summary-actions">
              <button
                className="btn btn-primary checkout-btn"
                onClick={handleCheckout}
              >
                Proceed to Checkout
              </button>

              <div className="cart-buttons">
                <Link to="/products" className="btn btn-outline">
                  Continue Shopping
                </Link>
                <button
                  className="btn btn-outline clear-cart-btn"
                  onClick={handleClearCart}
                >
                  Clear Cart
                </button>
              </div>
            </div>

            <div className="cart-notes">
              <p>
                <FaTruck />
                {settings && parseFloat(settings.free_delivery_threshold) > 0 ? (
                  <> Free delivery on orders over {parseFloat(settings.free_delivery_threshold)} TND</>
                ) : (
                  <> Delivery fee: {parseFloat(settings?.delivery_fee || 7)} TND</>
                )}
              </p>
              <p><FaMoneyBillWave /> Cash on delivery available</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Cart;
