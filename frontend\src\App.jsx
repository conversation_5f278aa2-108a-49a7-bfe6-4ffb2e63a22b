import { useState, useEffect, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import './App.css';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import MaintenanceMode from './components/layout/MaintenanceMode';

// Page Components
import Home from './pages/Home';
import ProductList from './pages/ProductList';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import Login from './pages/Login';
import Register from './pages/Register';
import Profile from './pages/Profile';
import OrderHistory from './pages/OrderHistory';
import OrderDetail from './pages/OrderDetail';
import AboutUs from './pages/AboutUs';
import ContactUs from './pages/ContactUs';
import Packs from './pages/Packs';
import PackDetail from './pages/PackDetail';
import NotFound from './pages/NotFound';

// Admin Components
import AdminDashboard from './pages/admin/Dashboard';
import AdminProducts from './pages/admin/Products';
import AdminCategories from './pages/admin/Categories';
import AdminOrders from './pages/admin/Orders';
import AdminOrderDetail from './pages/admin/OrderDetail';
import AdminSettings from './pages/admin/Settings';

// Context Providers
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import { SiteSettingsProvider, useSiteSettings } from './contexts/SiteSettingsContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Protected Route Component
const ProtectedRoute = ({ children, requireAdmin = false }) => {
  const { isAuthenticated, isAdmin, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <div className="loading-spinner">Loading...</div>;
  }

  if (!isAuthenticated) {
    // Determine the message based on the route
    let message = 'Please log in to access this page';
    if (location.pathname === '/checkout') {
      message = 'Please log in to proceed with checkout';
    }

    return <Navigate
      to="/login"
      state={{
        from: location.pathname,
        message: message
      }}
      replace
    />;
  }

  if (requireAdmin && !isAdmin) {
    return <Navigate to="/" replace />;
  }

  return children;
};

// Maintenance Mode Wrapper
const MaintenanceModeWrapper = ({ children }) => {
  const { settings, loading, isMaintenanceMode } = useSiteSettings();

  if (loading) {
    return (
      <div className="loading-screen">
        <div className="loading-spinner"></div>
        <p>Loading site settings...</p>
      </div>
    );
  }

  if (isMaintenanceMode()) {
    return <MaintenanceMode />;
  }

  return children;
};

// ScrollToTop component
const ScrollToTop = () => {
  const { pathname } = useLocation();
  const prevPathRef = useRef(pathname);

  useEffect(() => {
    // Only scroll if we're navigating to a different page
    if (prevPathRef.current !== pathname) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
      prevPathRef.current = pathname;
    }
  }, [pathname]);

  return null;
};

function App() {
  return (
    <Router>
      <ScrollToTop />
      <ThemeProvider>
        <SiteSettingsProvider>
          <AuthProvider>
            <CartProvider>
                <MaintenanceModeWrapper>
                  <div className="app">
                    <Header />
                    <main className="main-content">
                      <Routes>
                        {/* Public Routes */}
                        <Route path="/" element={<Home />} />
                        <Route path="/products" element={<ProductList />} />
                        <Route path="/products/category/:categoryId" element={<ProductList />} />
                        <Route path="/products/:productId" element={<ProductDetail />} />
                        <Route path="/cart" element={<Cart />} />
                        <Route path="/login" element={<Login />} />
                        <Route path="/register" element={<Register />} />
                        <Route path="/about" element={<AboutUs />} />
                        <Route path="/contact" element={<ContactUs />} />
                        <Route path="/packs" element={<Packs />} />
                        <Route path="/packs/:packId" element={<PackDetail />} />

                        {/* Checkout - Requires authentication */}
                        <Route path="/checkout" element={
                          <ProtectedRoute>
                            <Checkout />
                          </ProtectedRoute>
                        } />
                        <Route path="/orders/:orderId" element={<OrderDetail />} />

                        {/* Protected User Routes */}
                        <Route path="/profile" element={
                          <ProtectedRoute>
                            <Profile />
                          </ProtectedRoute>
                        } />

                        <Route path="/orders" element={
                          <ProtectedRoute>
                            <OrderHistory />
                          </ProtectedRoute>
                        } />

                        {/* Admin Routes */}
                        <Route path="/admin" element={
                          <ProtectedRoute requireAdmin={true}>
                            <AdminDashboard />
                          </ProtectedRoute>
                        } />
                        <Route path="/admin/products" element={
                          <ProtectedRoute requireAdmin={true}>
                            <AdminProducts />
                          </ProtectedRoute>
                        } />
                        <Route path="/admin/categories" element={
                          <ProtectedRoute requireAdmin={true}>
                            <AdminCategories />
                          </ProtectedRoute>
                        } />
                        <Route path="/admin/orders" element={
                          <ProtectedRoute requireAdmin={true}>
                            <AdminOrders />
                          </ProtectedRoute>
                        } />
                        <Route path="/admin/orders/:orderId" element={
                          <ProtectedRoute requireAdmin={true}>
                            <AdminOrderDetail />
                          </ProtectedRoute>
                        } />
                        <Route path="/admin/settings" element={
                          <ProtectedRoute requireAdmin={true}>
                            <AdminSettings />
                          </ProtectedRoute>
                        } />

                        {/* 404 Route */}
                        <Route path="*" element={<NotFound />} />
                      </Routes>
                    </main>
                    <Footer />
                  </div>
                </MaintenanceModeWrapper>
            </CartProvider>
          </AuthProvider>
        </SiteSettingsProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;
