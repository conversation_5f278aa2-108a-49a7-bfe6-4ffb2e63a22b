import { useState, useEffect } from 'react';
import { useParams, useLocation, Link } from 'react-router-dom';
import { ordersAPI } from '../services/api';
import {
  FaCheckCircle,
  FaShoppingBag,
  FaBox,
  FaBoxOpen,
  FaShippingFast,
  FaTimesCircle,
  FaArrowLeft,
  FaShoppingCart,
  FaUndo,
  FaMoneyBillWave,
  FaExclamationTriangle
} from 'react-icons/fa';
import '../styles/order-detail.css';

const OrderDetail = () => {
  const { orderId } = useParams();
  const location = useLocation();

  // Check if this is an order confirmation page
  const isOrderConfirmation = location.state?.orderConfirmation || false;
  const orderDataFromState = location.state?.orderData;
  const isGuestOrder = location.state?.isGuestOrder || false;
  const customerEmail = location.state?.customerEmail;

  const [order, setOrder] = useState(orderDataFromState || null);
  const [orderNumber, setOrderNumber] = useState(null);
  const [loading, setLoading] = useState(!orderDataFromState);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If we already have order data from state, no need to fetch the order
    // but we still need to fetch all orders to calculate the order number
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch the specific order if not provided in state
        if (!orderDataFromState) {
          try {
            const orderResponse = await ordersAPI.getById(orderId);
            const orderData = orderResponse.data;



            setOrder(orderData);
          } catch (orderError) {
            console.error('Error fetching order:', orderError);
            // For guest users, if they can't access the order, show a message
            setError('Order not found or access denied. Please check your order confirmation email for details.');
            setLoading(false);
            return;
          }
        }

        // Fetch all orders to calculate the order number
        const allOrdersResponse = await ordersAPI.getAll();
        const orders = allOrdersResponse.data.results || [];

        // Sort orders by creation date (oldest first)
        const sortedOrders = [...orders].sort((a, b) =>
          new Date(a.created_at) - new Date(b.created_at)
        );

        // Find the position of this order in chronologically sorted orders
        const currentOrderId = orderDataFromState ? orderDataFromState.id : orderId;
        const position = sortedOrders.findIndex(o => o.id.toString() === currentOrderId.toString());

        // Set the order number (position + 1)
        if (position !== -1) {
          setOrderNumber((position + 1).toString());
        } else {
          // Fallback to order ID if not found in the list
          setOrderNumber(currentOrderId);
        }

      } catch (err) {
        console.error('Error fetching order data:', err);
        setError('Failed to load order details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [orderId, orderDataFromState]);

  // Format date
  const formatDate = (dateInput) => {
    if (!dateInput) return 'N/A';

    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);

    // Check if date is valid
    if (isNaN(date.getTime())) return 'Invalid date';

    // For created_at, include time
    if (typeof dateInput === 'string' && dateInput.includes('T')) {
      const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
      return date.toLocaleDateString(undefined, options);
    }

    // For shipping_date and delivery_date, show only the date
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  // Get order number - now using the calculated orderNumber state
  const getOrderNumber = () => {
    // If we have a calculated order number, use that
    if (orderNumber) {
      return orderNumber;
    }

    // If we have order data from state with an orderNumber property, use that as fallback
    if (orderDataFromState && orderDataFromState.orderNumber) {
      return orderDataFromState.orderNumber;
    }

    // Otherwise, use the order ID as a last resort fallback
    return orderId;
  };

  // Calculate total quantity of items in an order
  const getTotalItemCount = (order) => {
    if (!order || !order.items || !Array.isArray(order.items) || order.items.length === 0) {
      return 0;
    }

    let total = 0;
    for (const item of order.items) {
      if (item && typeof item.quantity !== 'undefined') {
        const quantity = Number(item.quantity);
        if (!isNaN(quantity) && quantity > 0) {
          total += quantity;
        }
      }
    }

    return total;
  };

  // Get status badge info with icon and class
  const getStatusInfo = (status) => {
    switch (status) {
      case 'delivered':
        return {
          class: 'status-delivered',
          icon: <FaCheckCircle />,
          label: 'Delivered'
        };
      case 'shipped':
        return {
          class: 'status-shipped',
          icon: <FaShippingFast />,
          label: 'Shipped'
        };
      case 'ready_for_pickup':
        return {
          class: 'status-ready-for-pickup',
          icon: <FaBoxOpen />,
          label: 'Ready for Pickup'
        };
      case 'processing':
        return {
          class: 'status-processing',
          icon: <FaBox />,
          label: 'Processing'
        };
      case 'cancelled':
        return {
          class: 'status-cancelled',
          icon: <FaTimesCircle />,
          label: 'Cancelled'
        };
      case 'returned':
        return {
          class: 'status-returned',
          icon: <FaUndo />,
          label: 'Returned'
        };
      case 'refunded':
        return {
          class: 'status-refunded',
          icon: <FaMoneyBillWave />,
          label: 'Refunded'
        };
      case 'pending':
      default:
        return {
          class: 'status-pending',
          icon: <FaShoppingBag />,
          label: 'Pending'
        };
    }
  };

  if (loading) {
    return (
      <div className="order-detail-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading your order details...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="order-detail-page">
        <div className="error-container">
          <FaTimesCircle size={50} color="#d32f2f" />
          <h2>Order Not Found</h2>
          <p>{error || 'We couldn\'t find the order you\'re looking for. It may have been removed or the ID is incorrect.'}</p>
          <Link to="/orders" className="btn btn-primary">
            <FaArrowLeft /> Back to My Orders
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="order-detail-page">
      {isOrderConfirmation && (
        <div className="order-confirmation">
          <div className="confirmation-icon">
            <FaCheckCircle />
          </div>
          <h1>Order Successfully Placed!</h1>
          <p>Thank you for your order. We've received your order and will begin processing it right away.</p>

          {isGuestOrder && customerEmail && (
            <div className="guest-order-info">
              <p><strong>Order Confirmation:</strong></p>
              <ul>
                <li>A confirmation email has been sent to <strong>{customerEmail}</strong></li>
                <li>Your Order ID is <strong>#{orderId}</strong></li>
                <li>Save this Order ID for your records</li>
              </ul>
            </div>
          )}

          {!isGuestOrder && (
            <p>You'll receive a confirmation email shortly with all the details.</p>
          )}
        </div>
      )}

      <div className="order-detail-header">
        <div className="order-title-section">
          <h1>{isOrderConfirmation ? 'Order Details' : `Order #${getOrderNumber()}`}</h1>
        </div>
      </div>

      <div className="order-detail-container">
        <div className="order-info-section">
          <div className="order-info-card">
            <h2>Order Information</h2>
            <div className="info-content">
              <div className="info-row">
                <span className="info-label">Order Number:</span>
                <span className="info-value">#{getOrderNumber()}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Order Date:</span>
                <span className="info-value">{formatDate(order.created_at)}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Total Items:</span>
                <span className="info-value">{getTotalItemCount(order)} item{getTotalItemCount(order) !== 1 ? 's' : ''}</span>
              </div>
              <div className="info-row">
                <span className="info-label">Total Amount:</span>
                <span className="info-value total-amount">{order.total_amount || order.total_price} DT</span>
              </div>
            </div>
          </div>

          <div className="order-info-card">
            <h2>Shipping Information</h2>
            <div className="info-content">
              <div className="shipping-address">
                {order.shipping_address ? (
                  <div className="address-block">
                    {order.shipping_address.split('\n').map((line, index) => (
                      <div key={index} className="address-line">{line}</div>
                    ))}
                  </div>
                ) : (
                  <div className="address-block">
                    <div className="address-line">{order.full_name}</div>
                    <div className="address-line">{order.address}</div>
                    <div className="address-line">{order.city}, {order.state}</div>
                    <div className="address-line">Email: {order.email}</div>
                    <div className="address-line">Phone: {order.phone_number || order.phone}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="order-items-section">
          <div className="items-section-header">
            <h2>Order Items</h2>
            <div className="items-count">
              {getTotalItemCount(order)} item{getTotalItemCount(order) !== 1 ? 's' : ''}
            </div>
          </div>

          <div className="order-items-container">

            {order.items && order.items.length > 0 ? (
              <div className="items-grid">
                {order.items.map((item) => {
                  // Enhanced pack detection - check for pack metadata stored during checkout
                  const isPackItem = item.pack ||
                                   (item.type === 'pack') ||
                                   (item.pack_id) ||
                                   (item.pack_name) ||
                                   (item.is_pack) ||
                                   (item.item_type === 'pack');



                  return (
                    <div key={item.id} className="item-card">
                      <div className="item-image-section">
                        <div className="item-image">
                          {isPackItem ? (
                            <div className="item-placeholder pack-placeholder">
                              🎁
                            </div>
                          ) : (
                            item.product?.image ? (
                              <img
                                src={item.product.image}
                                alt={item.product?.name || 'Product'}
                                onError={(e) => {
                                  e.target.src = '/images/product-placeholder.jpg';
                                }}
                              />
                            ) : (
                              <div className="item-placeholder">
                                📦
                              </div>
                            )
                          )}
                        </div>
                      </div>

                      <div className="item-content">
                        <div className="item-header">
                          <h3 className="item-title">
                            {isPackItem ? (
                              item.pack ? (
                                <Link to={`/packs/${item.pack.id || item.pack_id}`} className="product-link">
                                  {item.pack.name || item.pack_name || item.name || item.product_name}
                                </Link>
                              ) : (
                                item.pack_id ? (
                                  <Link to={`/packs/${item.pack_id}`} className="product-link">
                                    {item.pack_name || item.name || item.product_name || 'Pack'}
                                  </Link>
                                ) : (
                                  <span>{item.pack_name || item.name || item.product_name || 'Pack'}</span>
                                )
                              )
                            ) : (
                              item.product ? (
                                <Link to={`/products/${item.product.id}`} className="product-link">
                                  {item.product.name}
                                </Link>
                              ) : (
                                <span>{item.product_name || item.name || 'Product'}</span>
                              )
                            )}
                          </h3>
                          <p className="item-category">
                            {isPackItem ? 'Product Pack' : (item.product?.category_name || item.product_category || 'Product')}
                          </p>
                        </div>

                        <div className="item-details">
                          <div className="detail-row">
                            <span className="detail-label">
                              {isPackItem ? 'Pack Price:' : 'Unit Price:'}
                            </span>
                            <span className="detail-value price">
                              {isPackItem ? (item.pack_price || item.price) : item.price} DT
                            </span>
                          </div>

                          <div className="detail-row">
                            <span className="detail-label">Quantity:</span>
                            <span className="detail-value quantity">×{item.quantity}</span>
                          </div>

                          <div className="detail-row total-row">
                            <span className="detail-label">Total:</span>
                            <span className="detail-value total">
                              {((isPackItem ? (parseFloat(item.pack_price) || parseFloat(item.price)) : parseFloat(item.price)) * item.quantity).toFixed(2)} DT
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="empty-items">
                <div className="empty-icon">
                  <FaBox />
                </div>
                <h3>No Items Found</h3>
                <p>This order doesn't contain any items.</p>
              </div>
            )}
          </div>

          <div className="order-summary">
            <h3 className="summary-title">Order Summary</h3>
            {order.subtotal && (
              <div className="order-summary-row">
                <span>Subtotal:</span>
                <span>{order.subtotal} DT</span>
              </div>
            )}
            {order.tax_amount && (
              <div className="order-summary-row">
                <span>Tax {order.tax_percentage ? `(${order.tax_percentage}%)` : ''}:</span>
                <span>{order.tax_amount} DT</span>
              </div>
            )}
            {order.delivery_fee && (
              <div className="order-summary-row">
                <span>Shipping:</span>
                {parseFloat(order.delivery_fee) > 0 ? (
                  <span>{order.delivery_fee} DT</span>
                ) : (
                  <span className="free-shipping">Free</span>
                )}
              </div>
            )}
            {order.discount && order.discount > 0 && (
              <div className="order-summary-row discount">
                <span>Discount:</span>
                <span>-{order.discount} DT</span>
              </div>
            )}
            <div className="order-summary-row total">
              <span>Total:</span>
              <span>{order.total_amount || order.total_price} DT</span>
            </div>
          </div>
        </div>
      </div>

      <div className="order-actions">
        {isGuestOrder ? (
          <Link to="/products" className="btn btn-primary">
            <FaShoppingCart /> Continue Shopping
          </Link>
        ) : (
          <>
            <Link to="/orders" className="btn btn-outline">
              <FaArrowLeft /> Back to Orders
            </Link>
            {isOrderConfirmation && (
              <Link to="/products" className="btn btn-primary">
                <FaShoppingCart /> Continue Shopping
              </Link>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default OrderDetail;
