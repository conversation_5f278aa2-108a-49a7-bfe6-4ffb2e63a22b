import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ordersAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import {
  FaShoppingBag,
  FaBox,
  FaBoxOpen,
  FaShippingFast,
  FaCheckCircle,
  FaTimesCircle,
  FaUndo,
  FaMoneyBillWave
} from 'react-icons/fa';
import { isPackItem, getItemDisplayName } from '../utils/packMapping';
import '../styles/order-history.css';

const OrderHistory = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { isAuthenticated, currentUser } = useAuth();

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await ordersAPI.getAll();
        const ordersData = response.data.results || [];



        setOrders(ordersData);

      } catch (err) {
        console.error('Error fetching orders:', err);
        setError('Failed to load your orders. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format order number - using order position in chronological order (oldest first)
  const formatOrderNumber = (order, allOrders) => {
    // Find the position of this order in chronologically sorted orders
    const sortedOrders = [...allOrders].sort((a, b) =>
      new Date(a.created_at) - new Date(b.created_at)
    );
    const position = sortedOrders.findIndex(o => o.id === order.id);
    // Return the position + 1 as the order number
    return (position + 1).toString();
  };

  // Calculate total quantity of items in an order
  const getTotalItemCount = (order) => {
    if (!order || !order.items || !Array.isArray(order.items) || order.items.length === 0) {
      return 0;
    }

    let total = 0;
    for (const item of order.items) {
      if (item && typeof item.quantity !== 'undefined') {
        const quantity = Number(item.quantity);
        if (!isNaN(quantity) && quantity > 0) {
          total += quantity;
        }
      }
    }

    return total;
  };

  // Get status badge class and icon
  const getStatusInfo = (status) => {
    switch (status) {
      case 'delivered':
        return {
          class: 'status-delivered',
          icon: <FaCheckCircle />,
          label: 'Delivered'
        };
      case 'shipped':
        return {
          class: 'status-shipped',
          icon: <FaShippingFast />,
          label: 'Shipped'
        };
      case 'ready_for_pickup':
        return {
          class: 'status-ready-for-pickup',
          icon: <FaBoxOpen />,
          label: 'Ready for Pickup'
        };
      case 'processing':
        return {
          class: 'status-processing',
          icon: <FaBox />,
          label: 'Processing'
        };
      case 'cancelled':
        return {
          class: 'status-cancelled',
          icon: <FaTimesCircle />,
          label: 'Cancelled'
        };
      case 'returned':
        return {
          class: 'status-returned',
          icon: <FaUndo />,
          label: 'Returned'
        };
      case 'refunded':
        return {
          class: 'status-refunded',
          icon: <FaMoneyBillWave />,
          label: 'Refunded'
        };
      case 'pending':
      default:
        return {
          class: 'status-pending',
          icon: <FaShoppingBag />,
          label: 'Pending'
        };
    }
  };

  if (loading) {
    return (
      <div className="order-history-page">
        <div className="order-history-header">
          <h1>My Orders</h1>
          <p>Track your order history and manage your purchases</p>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading your order history...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="order-history-page">
      <div className="order-history-header">
        <h1>My Orders</h1>
        <p>Track your order history and manage your purchases</p>
      </div>

      {error && (
        <div className="order-history-error">
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </div>
      )}

      {!error && orders.length === 0 ? (
        <div className="empty-orders">
          <div className="empty-orders-icon">
            <FaShoppingBag />
          </div>
          <h2>No orders yet</h2>
          <p>Your order history is empty. Start shopping to see your orders here!</p>
          <Link to="/products" className="btn btn-primary">
            Explore Products
          </Link>
        </div>
      ) : (
        <div className="order-history-container">
          <div className="order-grid">
            {orders.map((order, index) => (
              <div key={order.id} className="card order-card">
                {/* Order Header */}
                <div className="card-body">
                  <div className="card-title-link">
                    <h3 className="card-title">Order #{formatOrderNumber(order, orders)}</h3>
                  </div>
                  <p className="card-text order-date">
                    Placed on {formatDate(order.created_at)}
                  </p>

                  {/* Order Items Preview */}
                  <div className="order-items-preview">
                    {order.items && order.items.length > 0 ? (
                      <>
                        <div className="items-summary">
                          <div className="items-count">
                            <FaBox className="count-icon" />
                            <span className="count-text">
                              {getTotalItemCount(order)} item{getTotalItemCount(order) !== 1 ? 's' : ''}
                              ({order.items.length} product{order.items.length !== 1 ? 's' : ''})
                            </span>
                          </div>
                        </div>

                        <div className="items-list">
                          {order.items.slice(0, 4).map((item, index) => {
                            // Use utility functions to detect packs and get display names
                            const isPack = isPackItem(item, order.id);
                            const displayName = getItemDisplayName(item, order.id);

                            return (
                              <div key={item.id} className="item-preview-simple">
                                <div className="item-box">
                                  {isPack ? '🎁' : <FaBox />}
                                </div>
                                <div className="item-name-simple">
                                  {displayName}
                                </div>
                                <div className="item-quantity-simple">×{item.quantity}</div>
                              </div>
                            );
                          })}

                          {order.items.length > 4 && (
                            <div className="more-items-simple">
                              +{order.items.length - 4} more
                            </div>
                          )}
                        </div>
                      </>
                    ) : (
                      <div className="no-items-message">
                        <FaBox className="no-items-icon" />
                        <p>This order has no items</p>
                      </div>
                    )}
                  </div>

                  {/* Order Total */}
                  <div className="card-meta">
                    <div className="card-price order-total">
                      {order.total_amount || order.total_price} DT
                    </div>
                  </div>

                  {/* Order Actions */}
                  <div className="card-actions">
                    <button
                      className="btn btn-primary"
                      onClick={() => navigate(`/orders/${order.id}`, {
                        state: {
                          orderNumber: formatOrderNumber(order, orders)
                        }
                      })}
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderHistory;
