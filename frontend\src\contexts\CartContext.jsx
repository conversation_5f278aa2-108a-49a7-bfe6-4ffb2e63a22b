import { createContext, useState, useContext, useEffect, useRef, useMemo, useCallback } from 'react';
import { cartAPI } from '../services/api';
import { useAuth } from './AuthContext';
import { getGuestCart, isLocalStorageAvailable, clearGuestCart } from '../utils/guestCart';

// Create the context
const CartContext = createContext();

// Custom hook to use the cart context
export const useCart = () => {
  return useContext(CartContext);
};

// Provider component
export const CartProvider = ({ children }) => {
  // Initialize cart with a more robust default state
  const [cart, setCart] = useState(() => {
    // Try to load initial cart state from localStorage if available
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const guestCart = getGuestCart();
        if (guestCart && guestCart.items && guestCart.items.length > 0) {
          console.log('🔄 CartProvider: Initializing with guest cart:', guestCart);
          return guestCart;
        }
      } catch (error) {
        console.error('❌ Error loading initial guest cart:', error);
      }
    }
    return { items: [], total: 0 };
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated, loading: authLoading } = useAuth();
  const fetchingRef = useRef(false);
  const lastAuthState = useRef(isAuthenticated);
  const updatingQuantityRef = useRef(false); // Flag to prevent auto-refresh during quantity updates

  // Helper function to merge guest cart with server cart
  const mergeGuestCartWithServer = async (guestCart, serverCart) => {
    try {
      console.log('🔄 Starting cart merge process');
      console.log('Guest cart items:', guestCart.items);
      console.log('Server cart items:', serverCart.items);

      // For each item in guest cart, add it to server cart
      for (const guestItem of guestCart.items) {
        try {
          if (guestItem.type === 'pack' && guestItem.pack) {
            // Handle pack items - add pack directly to server cart
            console.log(`🎁 Adding pack ${guestItem.pack.id} with quantity ${guestItem.quantity}`);
            await cartAPI.addPackToCart(guestItem.pack, guestItem.quantity);
          } else if (guestItem.product) {
            // Handle regular product items
            const existingItem = serverCart.items.find(item =>
              item.product.id === guestItem.product.id
            );

            if (existingItem) {
              // Update quantity if item exists
              const newQuantity = existingItem.quantity + guestItem.quantity;
              console.log(`📝 Updating existing item ${guestItem.product.id} quantity to ${newQuantity}`);
              await cartAPI.updateQuantity(guestItem.product.id, newQuantity);
            } else {
              // Add new item if it doesn't exist
              console.log(`➕ Adding new item ${guestItem.product.id} with quantity ${guestItem.quantity}`);
              await cartAPI.addToCart(guestItem.product, guestItem.quantity);
            }
          }
        } catch (itemError) {
          console.error(`❌ Error processing item:`, itemError);
          // Continue with other items even if one fails
        }
      }

      // Fetch updated cart after merging
      console.log('🔄 Fetching updated cart after merge');
      const updatedResponse = await cartAPI.getCart();
      console.log('✅ Cart merge completed successfully');
      return updatedResponse.data;
    } catch (error) {
      console.error('❌ Error merging guest cart with server cart:', error);
      // Return server cart if merge fails
      return serverCart;
    }
  };





  // Debug: Log cart state changes
  useEffect(() => {
    console.log('🛒 Cart state changed:', {
      itemCount: cart?.items?.length || 0,
      total: cart?.total || 0,
      isAuthenticated,
      authLoading,
      cartLoading: loading
    });
  }, [cart, isAuthenticated, authLoading, loading]);

  // Load cart based on authentication status
  useEffect(() => {
    const fetchCart = async () => {
      console.log('🔄 CartContext: fetchCart triggered, isAuthenticated:', isAuthenticated, 'authLoading:', authLoading);
      console.log('🔄 Previous auth state:', lastAuthState.current, '→ Current:', isAuthenticated);

      // Wait for authentication to finish loading
      if (authLoading) {
        console.log('⏸️ CartContext: Auth still loading, waiting...');
        return;
      }

      // Prevent duplicate fetches
      if (fetchingRef.current) {
        console.log('⏸️ CartContext: Fetch already in progress, skipping');
        return;
      }

      try {
        fetchingRef.current = true;
        setLoading(true);
        setError(null);

        if (isAuthenticated) {
          // User is authenticated - fetch server cart and merge with guest cart if exists
          try {
            console.log('🔐 User authenticated, fetching server cart');
            const response = await cartAPI.getCart();
            let serverCart = response.data;
            console.log('📦 Server cart loaded:', serverCart);
            console.log('📦 Server cart items count:', serverCart?.items?.length || 0);
            console.log('📦 Server cart total:', serverCart?.total || 0);

            // Check if there's a guest cart to merge
            if (isLocalStorageAvailable()) {
              const guestCart = getGuestCart();
              console.log('👤 Guest cart found:', guestCart);
              if (guestCart.items.length > 0) {
                console.log('🔄 Merging guest cart with server cart');
                // Merge guest cart items with server cart
                serverCart = await mergeGuestCartWithServer(guestCart, serverCart);
                // Clear guest cart after successful merge
                clearGuestCart();
                console.log('🧹 Guest cart cleared after merge');
              } else {
                console.log('📭 Guest cart is empty, no merge needed');
              }
            }

            console.log('✅ Setting final cart state:', serverCart);
            setCart(serverCart);
          } catch (apiError) {
            console.error('❌ Error loading authenticated cart:', apiError);
            // Don't clear the cart immediately on API error - try guest cart as fallback
            console.log('🔄 API error, falling back to guest cart');
            if (isLocalStorageAvailable()) {
              const guestCart = getGuestCart();
              console.log('📦 Fallback guest cart loaded:', guestCart);
              setCart(guestCart);
            } else {
              setCart({ items: [], total: 0 });
            }
            setError('Failed to load server cart, using local cart: ' + (apiError.response?.data?.error || apiError.message));
          }
        } else {
          // User is not authenticated - use guest cart
          console.log('👤 User not authenticated, loading guest cart');
          if (isLocalStorageAvailable()) {
            const guestCart = getGuestCart();
            console.log('📦 Guest cart loaded:', guestCart);
            setCart(guestCart);
          } else {
            console.log('❌ localStorage not available, setting empty cart');
            setCart({ items: [], total: 0 });
          }
        }
      } catch (err) {
        console.error('Error loading cart:', err);
        setError('Failed to load cart: ' + err.message);
        // Try to preserve existing cart data on error instead of clearing
        console.log('🔄 General error, trying to preserve existing cart data');
        if (!cart || !cart.items || cart.items.length === 0) {
          // Only set empty cart if current cart is already empty
          if (isLocalStorageAvailable()) {
            const guestCart = getGuestCart();
            if (guestCart && guestCart.items && guestCart.items.length > 0) {
              console.log('📦 Using guest cart as fallback:', guestCart);
              setCart(guestCart);
            } else {
              setCart({ items: [], total: 0 });
            }
          } else {
            setCart({ items: [], total: 0 });
          }
        }
      } finally {
        fetchingRef.current = false;
        setLoading(false);
        // Update the last auth state
        lastAuthState.current = isAuthenticated;
      }
    };

    // Add a small delay to ensure authentication state has stabilized
    const timeoutId = setTimeout(() => {
      fetchCart();
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [isAuthenticated, authLoading]);

  // Add window focus listener to refresh cart when user returns to tab
  useEffect(() => {
    const handleFocus = () => {
      console.log('🔄 Window focused, checking if cart refresh needed');
      // Don't refresh if we're currently updating quantities
      if (updatingQuantityRef.current) {
        console.log('⏸️ Skipping focus refresh - quantity update in progress');
        return;
      }

      console.log('🔄 Window focused, refreshing cart');
      // Small delay to ensure any background sync has completed
      setTimeout(() => {
        if (!authLoading && !fetchingRef.current && !updatingQuantityRef.current) {
          if (isAuthenticated) {
            // Refresh server cart
            cartAPI.getCart().then(response => {
              console.log('📦 Refreshed cart on focus:', response.data);
              setCart(response.data);
            }).catch(error => {
              console.error('❌ Error refreshing cart on focus:', error);
            });
          } else {
            // Refresh guest cart
            const guestCart = getGuestCart();
            console.log('📦 Refreshed guest cart on focus:', guestCart);
            setCart(guestCart);
          }
        }
      }, 100);
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [isAuthenticated, authLoading]);

  // Periodic cart sync (every 60 seconds) to prevent data loss - less aggressive
  useEffect(() => {
    if (authLoading) return;

    const syncInterval = setInterval(() => {
      console.log('🔄 Periodic cart sync');
      // Don't sync if we're currently updating quantities or fetching
      if (!fetchingRef.current && !updatingQuantityRef.current) {
        if (isAuthenticated) {
          // Sync with server cart
          cartAPI.getCart().then(response => {
            const serverCart = response.data;
            // Only update if server cart has different data
            if (JSON.stringify(serverCart) !== JSON.stringify(cart)) {
              console.log('📦 Server cart changed, updating:', serverCart);
              setCart(serverCart);
            }
          }).catch(error => {
            console.error('❌ Error in periodic sync:', error);
          });
        } else {
          // Sync with localStorage
          const guestCart = getGuestCart();
          if (JSON.stringify(guestCart) !== JSON.stringify(cart)) {
            console.log('📦 Guest cart changed, updating:', guestCart);
            setCart(guestCart);
          }
        }
      } else {
        console.log('⏸️ Skipping periodic sync - operation in progress');
      }
    }, 60000); // 60 seconds - less aggressive

    return () => clearInterval(syncInterval);
  }, [isAuthenticated, authLoading]); // Removed cart dependency to prevent restart on every cart change

  // Add item to cart
  const addToCart = async (product, quantity = 1) => {
    try {
      setError(null);
      setLoading(true);

      // Validate product
      if (!product) {
        const errorMsg = 'Invalid product';
        setError(errorMsg);
        throw new Error(errorMsg);
      }

      // Validate product.id
      if (!product.id) {
        const errorMsg = 'Invalid product ID';
        setError(errorMsg);
        throw new Error(errorMsg);
      }

      try {
        console.log('🛒 Adding to cart:', {
          productId: product.id,
          productName: product.name,
          quantity,
          isAuthenticated
        });

        if (isAuthenticated) {
          const response = await cartAPI.addToCart(product, quantity);
          console.log('✅ Item added to cart successfully:', response.data);
          setCart(response.data);
          return response.data;
        } else {
          // Handle guest cart
          const guestCart = getGuestCart();

          // Check if item already exists
          const existingItemIndex = guestCart.items.findIndex(item =>
            item.product && item.product.id === product.id
          );

          if (existingItemIndex >= 0) {
            // Update quantity
            guestCart.items[existingItemIndex].quantity += quantity;
          } else {
            // Add new item
            guestCart.items.push({
              id: Date.now(),
              product: product,
              quantity: quantity,
              type: 'product'
            });
          }

          // Calculate total
          guestCart.total = guestCart.items.reduce((sum, item) => {
            if (item.type === 'pack' && item.pack) {
              return sum + (parseFloat(item.pack.pack_price) * item.quantity);
            } else if (item.product) {
              return sum + (parseFloat(item.product.price) * item.quantity);
            }
            return sum;
          }, 0);

          // Save to localStorage
          if (isLocalStorageAvailable()) {
            localStorage.setItem('guestCart', JSON.stringify(guestCart));
          }

          // Update state
          setCart(guestCart);
          console.log('✅ Item added to guest cart:', guestCart);
          return guestCart;
        }
      } catch (apiError) {
        const errorMsg = apiError.response?.data?.error || 'Failed to add item to cart';
        setError(errorMsg);
        console.error('API error adding to cart:', apiError);
        throw apiError;
      }
    } catch (err) {
      console.error('Error in addToCart:', err);
      if (!error) {
        setError('Failed to add item to cart: ' + err.message);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add pack to cart
  const addPackToCart = async (pack, quantity = 1) => {
    try {
      setError(null);
      setLoading(true);

      // Validate pack
      if (!pack) {
        const errorMsg = 'Invalid pack';
        setError(errorMsg);
        throw new Error(errorMsg);
      }

      if (!pack.id) {
        const errorMsg = 'Invalid pack ID';
        setError(errorMsg);
        throw new Error(errorMsg);
      }

      try {
        console.log('🎁 Adding pack to cart:', {
          packId: pack.id,
          packName: pack.name,
          quantity,
          isAuthenticated
        });

        if (isAuthenticated) {
          // For authenticated users, add pack directly to server cart
          const response = await cartAPI.addPackToCart(pack, quantity);
          console.log('✅ Pack added to cart successfully:', response.data);
          setCart(response.data);
          return response.data;
        } else {
          // For guest users, store pack as pack in localStorage
          const guestCart = getGuestCart();

          // Check if pack already exists
          const existingPackIndex = guestCart.items.findIndex(item =>
            item.type === 'pack' && item.pack && item.pack.id === pack.id
          );

          if (existingPackIndex >= 0) {
            // Update pack quantity
            guestCart.items[existingPackIndex].quantity += quantity;
          } else {
            // Add new pack
            guestCart.items.push({
              id: Date.now(),
              pack: pack,
              quantity: quantity,
              type: 'pack'
            });
          }

          // Calculate total
          guestCart.total = guestCart.items.reduce((sum, item) => {
            if (item.type === 'pack' && item.pack) {
              return sum + (parseFloat(item.pack.pack_price) * item.quantity);
            } else if (item.product) {
              return sum + (parseFloat(item.product.price) * item.quantity);
            }
            return sum;
          }, 0);

          // Save to localStorage
          if (isLocalStorageAvailable()) {
            localStorage.setItem('guestCart', JSON.stringify(guestCart));
          }

          // Update state
          setCart(guestCart);
          console.log('✅ Pack added to guest cart:', guestCart);
          return guestCart;
        }
      } catch (apiError) {
        const errorMsg = apiError.response?.data?.error || 'Failed to add pack to cart';
        setError(errorMsg);
        console.error('API error adding pack to cart:', apiError);
        throw apiError;
      }
    } catch (err) {
      console.error('Error in addPackToCart:', err);
      if (!error) {
        setError('Failed to add pack to cart: ' + err.message);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update item quantity (for products)
  const updateQuantity = async (productId, quantity) => {
    try {
      setError(null);
      updatingQuantityRef.current = true; // Prevent auto-refresh during update
      // Don't set loading state for quantity updates to prevent UI refresh

      const response = await cartAPI.updateItemQuantity({ product_id: productId }, quantity);
      setCart(response.data);
      console.log('✅ Product quantity updated successfully');
    } catch (error) {
      console.error('❌ Error updating product quantity:', error);
      setError('Failed to update quantity: ' + (error.response?.data?.error || error.message));
    } finally {
      // Reset flag after a short delay to allow for UI updates
      setTimeout(() => {
        updatingQuantityRef.current = false;
      }, 1000);
    }
  };

  // Update pack quantity
  const updatePackQuantity = async (packId, quantity) => {
    try {
      setError(null);
      updatingQuantityRef.current = true; // Prevent auto-refresh during update
      // Don't set loading state for quantity updates to prevent UI refresh

      const response = await cartAPI.updateItemQuantity({ pack_id: packId }, quantity);
      setCart(response.data);
      console.log('✅ Pack quantity updated successfully');
    } catch (error) {
      console.error('❌ Error updating pack quantity:', error);
      setError('Failed to update pack quantity: ' + (error.response?.data?.error || error.message));
    } finally {
      // Reset flag after a short delay to allow for UI updates
      setTimeout(() => {
        updatingQuantityRef.current = false;
      }, 1000);
    }
  };

  // Remove item from cart
  const removeFromCart = async (productId) => {
    try {
      setError(null);
      setLoading(true);

      console.log('🗑️ Removing product from cart:', productId);
      console.log('🛒 Current cart state:', cart);

      const response = await cartAPI.removeItem({ product_id: productId });
      console.log('✅ Product removed from cart successfully:', response.data);
      setCart(response.data);
      return response.data;
    } catch (err) {
      console.error('❌ Error removing product from cart:', err);

      // If the item doesn't exist on the server, refresh the cart to sync state
      if (err.response?.status === 404) {
        console.log('🔄 Item not found on server, refreshing cart to sync state');
        try {
          // Force reload the cart from server to sync state
          if (isAuthenticated) {
            const response = await cartAPI.getCart();
            setCart(response.data);
            return response.data;
          } else {
            const guestCart = getGuestCart();
            setCart(guestCart);
            return guestCart;
          }
        } catch (refreshError) {
          console.error('❌ Error refreshing cart:', refreshError);
        }
      }

      setError('Failed to remove item from cart: ' + (err.response?.data?.error || err.message));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Remove pack from cart
  const removePackFromCart = async (packId) => {
    try {
      setError(null);
      setLoading(true);

      console.log('🗑️ Removing pack from cart:', packId);
      console.log('🛒 Current cart state:', cart);

      if (isAuthenticated) {
        // For authenticated users, remove pack from server cart
        try {
          const response = await cartAPI.removeItem({ pack_id: packId });
          console.log('✅ Pack removed from cart successfully:', response.data);
          setCart(response.data);
        } catch (apiError) {
          console.error('❌ Error removing pack from server cart:', apiError);

          // If the pack doesn't exist on the server, refresh the cart to sync state
          if (apiError.response?.status === 404) {
            console.log('🔄 Pack not found on server, refreshing cart to sync state');
            try {
              // Force reload the cart from server to sync state
              if (isAuthenticated) {
                const response = await cartAPI.getCart();
                setCart(response.data);
              } else {
                const guestCart = getGuestCart();
                setCart(guestCart);
              }
            } catch (refreshError) {
              console.error('❌ Error refreshing cart:', refreshError);
            }
          } else {
            throw apiError; // Re-throw non-404 errors
          }
        }
      } else {
        // For guest users, remove pack from localStorage
        const guestCart = getGuestCart();
        guestCart.items = guestCart.items.filter(item =>
          !(item.type === 'pack' && item.pack && item.pack.id === packId)
        );

        // Recalculate total
        guestCart.total = guestCart.items.reduce((sum, item) => {
          if (item.type === 'pack' && item.pack) {
            return sum + (parseFloat(item.pack.pack_price) * item.quantity);
          } else if (item.product) {
            return sum + (parseFloat(item.product.price) * item.quantity);
          }
          return sum;
        }, 0);

        // Save to localStorage
        if (isLocalStorageAvailable()) {
          localStorage.setItem('guestCart', JSON.stringify(guestCart));
        }

        setCart(guestCart);
      }

      console.log('✅ Pack removed from cart successfully');
    } catch (err) {
      setError('Failed to remove pack from cart');
      console.error('Error removing pack from cart:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Clear cart
  const clearCart = async () => {
    try {
      setError(null);
      setLoading(true);

      const response = await cartAPI.clearCart();
      setCart(response.data);

      return response.data;
    } catch (err) {
      setError('Failed to clear cart');
      console.error(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Manual refresh cart (useful for sync issues)
  const refreshCart = async () => {
    try {
      setError(null);
      setLoading(true);
      console.log('🔄 Manually refreshing cart');

      if (isAuthenticated) {
        const response = await cartAPI.getCart();
        console.log('📦 Refreshed cart data:', response.data);
        setCart(response.data);
        return response.data;
      } else {
        const guestCart = getGuestCart();
        console.log('📦 Refreshed guest cart:', guestCart);
        setCart(guestCart);
        return guestCart;
      }
    } catch (error) {
      console.error('❌ Error refreshing cart:', error);
      setError('Failed to refresh cart: ' + (error.response?.data?.error || error.message));
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Memoized calculations
  const itemCount = useMemo(() => {
    return cart.items ? cart.items.reduce((count, item) => count + item.quantity, 0) : 0;
  }, [cart.items]);

  // Context value
  const value = {
    cart,
    loading,
    error,
    addToCart,
    addPackToCart,
    updateQuantity,
    updatePackQuantity,
    removeFromCart,
    removePackFromCart,
    clearCart,
    refreshCart,

    itemCount,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
