.admin-order-detail-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: space-between;
}

.admin-header h1 {
  margin: 0;
  font-size: 1.8rem;
  flex-grow: 1;
}

.admin-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.admin-order-status-card {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.status-header h2 {
  margin: 0;
  font-size: 1.4rem;
}

.status-success-message {
  color: #28a745;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #d4edda;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.9rem;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-icon {
  font-size: 2rem;
}

.status-icon.pending {
  color: #ffc107;
}

.status-icon.processing {
  color: #17a2b8;
}

.status-icon.shipped {
  color: #007bff;
}

.status-icon.delivered {
  color: #28a745;
}

.status-icon.cancelled {
  color: #dc3545;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-label {
  font-size: 0.9rem;
  color: #6c757d;
}

.status-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.status-date {
  font-size: 0.85rem;
  color: #6c757d;
}

.status-update {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.status-select-wrapper {
  position: relative;
  display: inline-block;
}

.status-loading-spinner {
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

.admin-order-sections {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .admin-order-sections {
    grid-template-columns: 1fr 2fr;
  }
}

.admin-order-section {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.admin-order-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.section-header h2 {
  margin: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.admin-order-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 576px) {
  .admin-order-info-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.info-item {
  margin-bottom: 0.5rem;
}

.admin-order-items {
  overflow-x: auto;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th,
.admin-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.admin-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.admin-table tbody tr:hover {
  background-color: #f8f9fa;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
}

.product-category {
  font-size: 0.85rem;
  color: #666;
}

.btn-icon {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-icon:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.btn-danger {
  color: #dc3545;
}

.btn-danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.text-right {
  text-align: right;
}

/* Print styles */
@media print {
  .admin-header,
  .admin-order-status-card,
  .btn-icon,
  .no-print {
    display: none !important;
  }

  .admin-order-detail-page {
    padding: 0;
  }

  .print-header {
    display: block !important;
    margin-bottom: 20px;
    text-align: center;
  }

  .admin-order-sections {
    display: block;
  }

  .admin-order-section {
    box-shadow: none;
    border: 1px solid #ddd;
    margin-bottom: 20px;
    page-break-inside: avoid;
  }

  .admin-table {
    width: 100%;
    border-collapse: collapse;
  }

  .admin-table th,
  .admin-table td {
    border: 1px solid #ddd;
  }

  body {
    font-size: 12pt;
  }

  h1 {
    font-size: 18pt;
  }

  h2 {
    font-size: 14pt;
  }
}

.status-select {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  border: 1px solid #ced4da;
  background-color: #fff;
  font-size: 1rem;
}

.status-pending {
  border-color: #ffc107;
  color: #856404;
  background-color: #fff3cd;
}

.status-processing {
  border-color: #17a2b8;
  color: #0c5460;
  background-color: #d1ecf1;
}

.status-shipped {
  border-color: #007bff;
  color: #004085;
  background-color: #cce5ff;
}

.status-delivered {
  border-color: #28a745;
  color: #155724;
  background-color: #d4edda;
}

.status-cancelled {
  border-color: #dc3545;
  color: #721c24;
  background-color: #f8d7da;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  overflow: hidden;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin-top: 1rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}
