import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaFilter, FaUndo, FaChevronUp, FaChevronDown, FaTag, FaTimes, FaCheck } from 'react-icons/fa';

const EnhancedFilter = ({
  categories,
  currentCategoryId,
  filters,
  onFilterChange,
  onClearFilters,
  maxPriceLimit = 500,
  isMobileFilterOpen = false,
  onCloseMobileFilter
}) => {
  const navigate = useNavigate();
  const [priceRange, setPriceRange] = useState([0, maxPriceLimit]);
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    price: true,
    availability: true
  });

  // Check if any filters are active
  const hasActiveFilters = () => {
    return (
      currentCategoryId || // Category filter is active
      filters.minPrice || // Min price filter is active
      filters.maxPrice || // Max price filter is active
      filters.inStock || // Stock filter is active
      (priceRange[0] > 0 || priceRange[1] < maxPriceLimit) // Price range is modified
    );
  };

  // Initialize price range from filters
  useEffect(() => {
    const minPrice = filters.minPrice ? parseInt(filters.minPrice) : 0;
    const maxPrice = filters.maxPrice ? parseInt(filters.maxPrice) : maxPriceLimit;
    setPriceRange([minPrice, maxPrice]);
  }, [filters.minPrice, filters.maxPrice, maxPriceLimit]);

  // Reset price range when filters are cleared
  useEffect(() => {
    if (!filters.minPrice && !filters.maxPrice) {
      setPriceRange([0, maxPriceLimit]);
    }
  }, [filters.minPrice, filters.maxPrice, maxPriceLimit]);



  // Apply price range when the Apply button is clicked
  const handleApplyPriceFilter = () => {
    onFilterChange({
      target: { name: 'minPrice', value: priceRange[0].toString() }
    });
    onFilterChange({
      target: { name: 'maxPrice', value: priceRange[1].toString() }
    });
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };



  return (
    <>
      <aside className={`product-filters ${isMobileFilterOpen ? 'open' : ''}`}>
        <div className="filter-header">
          <h2>Filters</h2>
          <button
            className="filter-close-btn"
            onClick={onCloseMobileFilter}
          >
            <FaTimes />
          </button>
        </div>

        {/* Clear all filters button */}
        <button
          className="clear-filters-btn"
          onClick={onClearFilters}
          disabled={!hasActiveFilters()}
          title={hasActiveFilters() ? 'Clear filters' : 'No active filters'}
        >
          <FaUndo />
          Clear
        </button>

        {/* Categories filter */}
        <div className="filter-section">
          <div
            className="filter-section-header"
            onClick={() => toggleSection('categories')}
          >
            <h3>Categories</h3>
            {expandedSections.categories ? <FaChevronUp /> : <FaChevronDown />}
          </div>

          <div className={`filter-section-content ${expandedSections.categories ? 'expanded' : 'collapsed'}`}>
            <ul className="category-list">
              <li>
                <button
                  className={!currentCategoryId ? 'active' : ''}
                  onClick={() => navigate('/products')}
                >
                  <FaTag /> All Products
                </button>
              </li>
              {categories.map(category => (
                <li key={category.id}>
                  <button
                    className={currentCategoryId === category.id.toString() ? 'active' : ''}
                    onClick={() => navigate(`/products/category/${category.id}`)}
                  >
                    <FaTag /> {category.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Price range filter */}
        <div className="filter-section">
          <div
            className="filter-section-header"
            onClick={() => toggleSection('price')}
          >
            <h3>Price Range</h3>
            {expandedSections.price ? <FaChevronUp /> : <FaChevronDown />}
          </div>

          <div className={`filter-section-content ${expandedSections.price ? 'expanded' : 'collapsed'}`}>
            <div className="price-filter">
              {/* Simple Range Slider */}
              <div className="price-range-slider">
                <div className="range-values">
                  <span>{priceRange[0]} DT</span>
                  <span>{priceRange[1]} DT</span>
                </div>
                <div className="range-container">
                  <input
                    type="range"
                    min="0"
                    max={maxPriceLimit}
                    value={priceRange[0]}
                    onChange={(e) => {
                      const value = parseInt(e.target.value);
                      if (value <= priceRange[1]) {
                        setPriceRange([value, priceRange[1]]);
                      }
                    }}
                    className="range-slider range-min"
                  />
                  <input
                    type="range"
                    min="0"
                    max={maxPriceLimit}
                    value={priceRange[1]}
                    onChange={(e) => {
                      const value = parseInt(e.target.value);
                      if (value >= priceRange[0]) {
                        setPriceRange([priceRange[0], value]);
                      }
                    }}
                    className="range-slider range-max"
                  />
                </div>
              </div>

              <div className="price-inputs">
                <div className="price-input-group">
                  <label>Min</label>
                  <input
                    type="number"
                    name="minPrice"
                    value={priceRange[0]}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 0;
                      setPriceRange([value, priceRange[1]]);
                    }}
                    onBlur={(e) => {
                      const value = parseInt(e.target.value) || 0;
                      onFilterChange({
                        target: { name: 'minPrice', value: value.toString() }
                      });
                    }}
                    min="0"
                    max={priceRange[1]}
                  />
                  <span className="price-currency">DT</span>
                </div>

                <div className="price-input-group">
                  <label>Max</label>
                  <input
                    type="number"
                    name="maxPrice"
                    value={priceRange[1]}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 0;
                      setPriceRange([priceRange[0], value]);
                    }}
                    onBlur={(e) => {
                      const value = parseInt(e.target.value) || 0;
                      onFilterChange({
                        target: { name: 'maxPrice', value: value.toString() }
                      });
                    }}
                    min={priceRange[0]}
                    max={maxPriceLimit}
                  />
                  <span className="price-currency">DT</span>
                </div>
              </div>

              <button
                className="apply-price-btn"
                onClick={handleApplyPriceFilter}
              >
                <FaCheck /> Apply Price Filter
              </button>
            </div>
          </div>
        </div>

        {/* Availability filter */}
        <div className="filter-section">
          <div
            className="filter-section-header"
            onClick={() => toggleSection('availability')}
          >
            <h3>Availability</h3>
            {expandedSections.availability ? <FaChevronUp /> : <FaChevronDown />}
          </div>

          <div className={`filter-section-content ${expandedSections.availability ? 'expanded' : 'collapsed'}`}>
            <div className="availability-filter">
              <label className="switch-label">
                <span>In Stock Only</span>
                <label className="switch">
                  <input
                    type="checkbox"
                    name="inStock"
                    checked={filters.inStock}
                    onChange={onFilterChange}
                  />
                  <span className="slider"></span>
                </label>
              </label>
            </div>
          </div>
        </div>

        {/* Mobile apply filters button */}
        <button
          className="apply-filters-btn"
          onClick={onCloseMobileFilter}
        >
          Apply Filters
        </button>
      </aside>
    </>
  );
};

export default EnhancedFilter;
