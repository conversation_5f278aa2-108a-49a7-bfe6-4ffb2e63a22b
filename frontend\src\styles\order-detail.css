/* Order Detail Page Styles */
.order-detail-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: var(--font-family-sans);
  background-color: var(--background-light);
  min-height: 100vh;
}

/* Order Confirmation Section */
.order-confirmation {
  background: linear-gradient(135deg, var(--background-light), rgba(16, 185, 129, 0.1));
  border-radius: 12px;
  padding: 2.5rem;
  text-align: center;
  margin-bottom: 2rem;
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--success-color);
  position: relative;
  overflow: hidden;
}

.order-confirmation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, var(--success-color), var(--primary-color));
}

.confirmation-icon {
  font-size: 4rem;
  color: #4caf50;
  margin-bottom: 1rem;
  animation: scaleUp 0.5s ease-out;
}

@keyframes scaleUp {
  0% { transform: scale(0); opacity: 0; }
  70% { transform: scale(1.2); }
  100% { transform: scale(1); opacity: 1; }
}

.order-confirmation h1 {
  font-size: 2.2rem;
  font-weight: 600;
  color: #2e7d32;
  margin-bottom: 1rem;
}

.order-confirmation p {
  font-size: 1.1rem;
  color: #555;
  max-width: 600px;
  margin: 0 auto;
}

/* Order Header */
.order-detail-header {
  margin-bottom: 2rem;
  position: relative;
  background-color: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.order-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
}

.order-detail-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  position: relative;
}

.order-id {
  font-size: 0.9rem;
  color: #666;
  background-color: #f5f5f5;
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  border: 1px solid #eee;
}

.order-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Order date info styles removed */

.order-meta p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 1rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.status-processing {
  background-color: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.status-shipped {
  background-color: rgba(6, 182, 212, 0.1);
  color: var(--info-color);
  border: 1px solid var(--info-color);
}

.status-delivered {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
  font-weight: 600;
}

.status-cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

/* Order Container */
.order-detail-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Order Info Section */
.order-info-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-info-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.order-info-card h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  padding: 1.2rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  margin: 0;
}

.info-content {
  padding: 1.2rem;
}

.info-content p {
  margin-bottom: 0.8rem;
  color: #555;
  font-size: 0.95rem;
  line-height: 1.5;
}

.info-content p:last-child {
  margin-bottom: 0;
}

.info-content p strong {
  font-weight: 600;
  color: #333;
  display: inline-block;
  width: 120px;
}

/* Info row styling */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.info-value {
  color: #333;
  font-size: 0.9rem;
}

.info-value.total-amount {
  font-weight: 700;
  color: var(--primary-color, #e53935);
  font-size: 1.1rem;
}

/* Shipping address styling */
.shipping-address {
  margin-top: 0.5rem;
}

.address-block {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.address-line {
  margin: 0.25rem 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
}

.address-line:first-child {
  font-weight: 600;
  color: #333;
}

/* Payment Status */
.payment-status {
  display: inline-block;
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

.payment-status.paid {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.payment-status.pending {
  background-color: #fff8e1;
  color: #f57c00;
}



/* Order Items Section */
.order-items-section {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

/* Items section header */
.items-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.items-section-header h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.items-count {
  background-color: var(--primary-color, #e53935);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.order-items-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  padding: 1.2rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  margin: 0;
}

.order-items-container {
  padding: 1.5rem;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.item-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.item-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #d0d7de;
}

.item-image-section {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.item-image {
  width: 100%;
  height: 100%;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.item-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-size: 3rem;
  color: var(--gray);
}

.item-placeholder.pack-placeholder {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
}

.item-card:hover .item-image img {
  transform: scale(1.05);
}

.item-content {
  padding: 1.5rem;
}

.item-header {
  margin-bottom: 1rem;
}

.item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.product-link {
  color: #333;
  text-decoration: none;
  transition: color 0.3s ease;
}

.product-link:hover {
  color: var(--primary-color, #e53935);
}

.item-category {
  font-size: 0.9rem;
  color: var(--gray);
  margin: 0.25rem 0 0.5rem 0;
  font-style: italic;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.detail-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.detail-value.price {
  color: #666;
}

.detail-value.quantity {
  background-color: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.total-row {
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 2px solid #f0f0f0;
  border-bottom: none;
}

.detail-value.total {
  color: var(--primary-color, #e53935);
  font-size: 1.1rem;
  font-weight: 700;
}

.empty-items {
  text-align: center;
  padding: 3rem 1.5rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-items h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #495057;
}

.empty-items p {
  margin: 0;
  font-size: 0.9rem;
}




}

.basic-product-info {
  margin: 0.5rem 0;
  padding: 0.8rem;
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #eee;
}

.basic-product-info p {
  margin: 0.3rem 0;
  font-size: 0.95rem;
  color: #333;
}

.basic-product-info strong {
  font-weight: 600;
  color: #555;
  display: inline-block;
  width: 70px;
}

.product-details-section {
  margin-top: 0.8rem;
  padding-top: 0.5rem;
}

.product-description {
  margin-bottom: 0.8rem;
}

.product-description p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
}

.product-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.product-spec {
  font-size: 0.85rem;
  color: #555;
  background-color: #f5f5f5;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  border: 1px solid #eee;
}

.product-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.8rem;
  margin-top: 1rem;
}

.product-detail {
  display: flex;
  flex-direction: column;
  font-size: 0.9rem;
  background-color: #f9f9f9;
  padding: 0.8rem;
  border-radius: 6px;
  border: 1px solid #eee;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-detail:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);
}

.product-detail.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-weight: 600;
  color: #555;
  margin-bottom: 0.3rem;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  color: #333;
}

.detail-value.description {
  font-style: italic;
  color: #666;
  line-height: 1.5;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 4px;
  border-left: 3px solid #e53935;
}

.stock-status .in-stock {
  color: #4caf50;
  font-weight: 500;
}

.stock-status .out-of-stock {
  color: #f44336;
  font-weight: 500;
}

.no-details-message,
.no-items-message {
  padding: 1.5rem;
  background-color: #f5f5f5;
  border-radius: 8px;
  color: #666;
  font-style: italic;
  text-align: center;
  margin: 1rem 0;
  border: 1px dashed #ddd;
}



/* Order Summary */
.order-summary {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-top: 1px solid #eee;
  border-radius: 0 0 12px 12px;
}

.summary-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid #eee;
}

.order-summary-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  font-size: 1rem;
  color: #555;
}

.free-shipping {
  color: #4caf50;
  font-weight: 500;
}

.order-summary-row.discount {
  color: #e53935;
}

.order-summary-row.total {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  padding-top: 1rem;
  margin-top: 0.5rem;
  border-top: 1px dashed #ddd;
}

.order-summary-row.total span:last-child {
  color: #e53935;
}

/* Payment-related styles removed */

/* Order Actions */
.order-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.btn {
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-outline {
  background-color: transparent;
  color: #555;
  border: 1px solid #ddd;
}

.btn-outline:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.btn-primary {
  background-color: #e53935;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(229, 57, 53, 0.1);
  border-top: 4px solid #e53935;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container h2 {
  color: #d32f2f;
  margin-bottom: 1rem;
}

.error-container p {
  color: #666;
  margin-bottom: 1.5rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .order-detail-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .order-detail-page {
    padding: 1rem;
  }

  .items-section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.5rem 0;
  }

  .info-label {
    font-size: 0.85rem;
  }

  .info-value {
    font-size: 0.9rem;
    font-weight: 600;
  }

  .address-block {
    padding: 0.75rem;
  }

  .address-line {
    font-size: 0.85rem;
  }

  .order-items-header {
    display: none;
  }

  .order-item {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1.2rem;
    position: relative;
  }

  .order-item-image {
    width: 100%;
    height: 200px;
  }

  .order-item-header {
    flex-direction: column;
    gap: 1rem;
  }

  .order-item-price-info {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.5rem;
  }

  .product-details-grid {
    grid-template-columns: 1fr;
    margin-top: 1rem;
  }

  .product-detail {
    padding: 0.8rem;
  }

  .detail-label {
    font-size: 0.85rem;
  }

  .detail-value {
    font-size: 0.95rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .item-image-section {
    height: 150px;
  }

  .item-content {
    padding: 1rem;
  }

  .item-title {
    font-size: 1rem;
  }

  .quantity-info {
    flex-direction: row;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .order-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .order-actions .btn {
    width: 100%;
  }
}


