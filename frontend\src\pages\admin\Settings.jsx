import { useState, useEffect } from 'react';
import { useSettings } from '../../contexts/SettingsContext';

const AdminSettings = () => {
  const { settings, updateSettings, loading, error, fetchSettings } = useSettings();
  
  const [formData, setFormData] = useState({
    maintenance_mode: false,
    phone_number: '',
    email: '',
    facebook_url: '',
    instagram_url: '',
    tiktok_url: '',
    address: '',
  });
  const [formErrors, setFormErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [submitting, setSubmitting] = useState(false);
  
  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);
  
  // Update form data when settings change
  useEffect(() => {
    if (settings) {
      setFormData({
        maintenance_mode: settings.maintenance_mode || false,
        phone_number: settings.phone_number || '',
        email: settings.email || '',
        facebook_url: settings.facebook_url || '',
        instagram_url: settings.instagram_url || '',
        tiktok_url: settings.tiktok_url || '',
        address: settings.address || '',
      });
    }
  }, [settings]);
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // Clear success message when form is changed
    if (successMessage) {
      setSuccessMessage('');
    }
  };
  
  // Validate form
  const validateForm = () => {
    const errors = {};
    
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }
    
    if (formData.facebook_url && !isValidUrl(formData.facebook_url)) {
      errors.facebook_url = 'Facebook URL is invalid';
    }
    
    if (formData.instagram_url && !isValidUrl(formData.instagram_url)) {
      errors.instagram_url = 'Instagram URL is invalid';
    }
    
    if (formData.tiktok_url && !isValidUrl(formData.tiktok_url)) {
      errors.tiktok_url = 'TikTok URL is invalid';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Check if URL is valid
  const isValidUrl = (url) => {
    try {
      new URL(url);
      return true;
    } catch (err) {
      return false;
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      try {
        setSubmitting(true);
        
        await updateSettings(formData);
        
        setSuccessMessage('Settings updated successfully');
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
        
      } catch (err) {
        console.error('Error updating settings:', err);
        setFormErrors({ general: 'Failed to update settings' });
      } finally {
        setSubmitting(false);
      }
    }
  };
  
  if (loading && !settings) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading settings...</p>
      </div>
    );
  }
  
  return (
    <div className="admin-settings-page">
      <div className="admin-header">
        <h1>Site Settings</h1>
      </div>
      
      {error && (
        <div className="admin-error">
          <p>{error}</p>
          <button onClick={fetchSettings} className="btn btn-primary">
            Try Again
          </button>
        </div>
      )}
      
      {formErrors.general && (
        <div className="admin-error">
          <p>{formErrors.general}</p>
        </div>
      )}
      
      {successMessage && (
        <div className="admin-success">
          <p>{successMessage}</p>
        </div>
      )}
      
      <div className="admin-settings-container">
        <form className="admin-form" onSubmit={handleSubmit}>
          <div className="form-section">
            <h2>General Settings</h2>
            
            <div className="form-group">
              <label className="switch-label">
                <span>Maintenance Mode</span>
                <div className="switch">
                  <input
                    type="checkbox"
                    name="maintenance_mode"
                    checked={formData.maintenance_mode}
                    onChange={handleChange}
                  />
                  <span className="slider"></span>
                </div>
              </label>
              <p className="form-help">
                When enabled, the site will display a maintenance message to visitors.
                Only administrators will be able to access the site.
              </p>
            </div>
          </div>
          
          <div className="form-section">
            <h2>Contact Information</h2>
            
            <div className="form-group">
              <label htmlFor="phone_number" className="form-label">Phone Number</label>
              <input
                type="tel"
                id="phone_number"
                name="phone_number"
                className={`form-control ${formErrors.phone_number ? 'error' : ''}`}
                value={formData.phone_number}
                onChange={handleChange}
                placeholder="Enter phone number"
              />
              {formErrors.phone_number && <div className="form-error">{formErrors.phone_number}</div>}
            </div>
            
            <div className="form-group">
              <label htmlFor="email" className="form-label">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                className={`form-control ${formErrors.email ? 'error' : ''}`}
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter email address"
              />
              {formErrors.email && <div className="form-error">{formErrors.email}</div>}
            </div>
            
            <div className="form-group">
              <label htmlFor="address" className="form-label">Address</label>
              <textarea
                id="address"
                name="address"
                className={`form-control ${formErrors.address ? 'error' : ''}`}
                value={formData.address}
                onChange={handleChange}
                placeholder="Enter store address"
                rows="3"
              ></textarea>
              {formErrors.address && <div className="form-error">{formErrors.address}</div>}
            </div>
          </div>
          
          <div className="form-section">
            <h2>Social Media</h2>
            
            <div className="form-group">
              <label htmlFor="facebook_url" className="form-label">
                <i className="fab fa-facebook"></i> Facebook URL
              </label>
              <input
                type="url"
                id="facebook_url"
                name="facebook_url"
                className={`form-control ${formErrors.facebook_url ? 'error' : ''}`}
                value={formData.facebook_url}
                onChange={handleChange}
                placeholder="https://facebook.com/yourpage"
              />
              {formErrors.facebook_url && <div className="form-error">{formErrors.facebook_url}</div>}
            </div>
            
            <div className="form-group">
              <label htmlFor="instagram_url" className="form-label">
                <i className="fab fa-instagram"></i> Instagram URL
              </label>
              <input
                type="url"
                id="instagram_url"
                name="instagram_url"
                className={`form-control ${formErrors.instagram_url ? 'error' : ''}`}
                value={formData.instagram_url}
                onChange={handleChange}
                placeholder="https://instagram.com/youraccount"
              />
              {formErrors.instagram_url && <div className="form-error">{formErrors.instagram_url}</div>}
            </div>
            
            <div className="form-group">
              <label htmlFor="tiktok_url" className="form-label">
                <i className="fab fa-tiktok"></i> TikTok URL
              </label>
              <input
                type="url"
                id="tiktok_url"
                name="tiktok_url"
                className={`form-control ${formErrors.tiktok_url ? 'error' : ''}`}
                value={formData.tiktok_url}
                onChange={handleChange}
                placeholder="https://tiktok.com/@youraccount"
              />
              {formErrors.tiktok_url && <div className="form-error">{formErrors.tiktok_url}</div>}
            </div>
          </div>
          
          <div className="form-actions">
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={submitting}
            >
              {submitting ? 'Saving...' : 'Save Settings'}
            </button>
            <button 
              type="button" 
              className="btn btn-outline"
              onClick={() => {
                setFormData({
                  maintenance_mode: settings.maintenance_mode || false,
                  phone_number: settings.phone_number || '',
                  email: settings.email || '',
                  facebook_url: settings.facebook_url || '',
                  instagram_url: settings.instagram_url || '',
                  tiktok_url: settings.tiktok_url || '',
                  address: settings.address || '',
                });
                setFormErrors({});
              }}
              disabled={submitting}
            >
              Reset
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminSettings;
