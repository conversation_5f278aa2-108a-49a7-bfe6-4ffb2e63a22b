import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { packsAPI, categoriesAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';
import PackCard from '../components/packs/PackCard';
import { FaArrowRight, FaSpinner, FaExclamationTriangle, FaChevronLeft, FaChevronRight, FaShoppingBag, FaGift } from 'react-icons/fa';
import '../styles/home.css';

const Home = () => {
  const { addToCart } = useCart();
  const [featuredPacks, setFeaturedPacks] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [addingToCart, setAddingToCart] = useState({});

  // Category slider state
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef(null);
  const autoSlideRef = useRef(null);

  // Handle add to cart
  const handleAddToCart = async (product) => {
    setAddingToCart(prev => ({ ...prev, [product.id]: true }));
    try {
      await addToCart(product, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingToCart(prev => ({ ...prev, [product.id]: false }));
    }
  };

  // Category slider functions
  const nextSlide = () => {
    if (categories.length > 0) {
      setCurrentSlide((prev) => (prev + 1) % categories.length);
    }
  };

  const prevSlide = () => {
    if (categories.length > 0) {
      setCurrentSlide((prev) => (prev - 1 + categories.length) % categories.length);
    }
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  // Auto-slide functionality
  useEffect(() => {
    if (categories.length > 1) {
      autoSlideRef.current = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % categories.length);
      }, 5000); // Change slide every 5 seconds

      return () => {
        if (autoSlideRef.current) {
          clearInterval(autoSlideRef.current);
        }
      };
    }
  }, [categories.length]);

  // Pause auto-slide on hover
  const pauseAutoSlide = () => {
    if (autoSlideRef.current) {
      clearInterval(autoSlideRef.current);
    }
  };

  const resumeAutoSlide = () => {
    if (categories.length > 1) {
      autoSlideRef.current = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % categories.length);
      }, 5000);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch featured packs
        const packsResponse = await packsAPI.getFeatured();
        // Only take up to 4 featured packs for a cleaner design
        const featuredPacksData = packsResponse.data.results || packsResponse.data || [];
        setFeaturedPacks(featuredPacksData.slice(0, 4));

        // Fetch categories
        const categoriesResponse = await categoriesAPI.getAll();

        // Handle different response formats
        let categoriesData = [];
        if (Array.isArray(categoriesResponse.data)) {
          categoriesData = categoriesResponse.data;
        } else if (categoriesResponse.data && categoriesResponse.data.results && Array.isArray(categoriesResponse.data.results)) {
          categoriesData = categoriesResponse.data.results;
        } else if (typeof categoriesResponse.data === 'object') {
          // If it's an object but not an array, convert to array
          categoriesData = Object.values(categoriesResponse.data);
        }

        // Show all categories in the slider
        setCategories(categoriesData);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching home page data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="home-page">
        <div className="loading-container">
          <FaSpinner className="loading-spinner" />
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="home-page">
        <div className="error-container">
          <FaExclamationTriangle className="error-icon" />
          <h2>Something went wrong</h2>
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* Category Slider Section */}
      <section className="category-slider-section">
        <div className="category-slider-container"
             onMouseEnter={pauseAutoSlide}
             onMouseLeave={resumeAutoSlide}>

          {loading ? (
            <div className="slider-loading">
              <FaSpinner className="loading-spinner" />
              <p>Loading categories...</p>
            </div>
          ) : categories.length > 0 ? (
            <>
              {/* Slider */}
              <div className="category-slider" ref={sliderRef}>
                <div
                  className="category-slides"
                  style={{
                    transform: `translateX(-${currentSlide * 100}%)`,
                    transition: 'transform 0.5s ease-in-out'
                  }}
                >
                  {categories.map((category, index) => (
                    <div key={category.id} className="category-slide">
                      <div className="category-slide-content">
                        <div className="category-slide-image">
                          {category.image ? (
                            <img
                              src={category.image}
                              alt={category.name}
                              className="category-image"
                            />
                          ) : (
                            <div className="category-image-placeholder">
                              <FaShoppingBag size={60} />
                            </div>
                          )}
                        </div>
                        <div className="category-slide-info">
                          <h2 className="category-title">{category.name}</h2>
                          <p className="category-description">
                            {category.description || `Discover our premium ${category.name.toLowerCase()} collection`}
                          </p>
                          <Link
                            to={`/products/category/${category.id}`}
                            className="category-shop-btn"
                          >
                            <FaShoppingBag />
                            Shop Now
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Navigation Arrows */}
              {categories.length > 1 && (
                <>
                  <button
                    className="slider-nav prev"
                    onClick={prevSlide}
                    aria-label="Previous category"
                  >
                    <FaChevronLeft />
                  </button>
                  <button
                    className="slider-nav next"
                    onClick={nextSlide}
                    aria-label="Next category"
                  >
                    <FaChevronRight />
                  </button>
                </>
              )}

              {/* Dots Indicator */}
              {categories.length > 1 && (
                <div className="slider-dots">
                  {categories.map((_, index) => (
                    <button
                      key={index}
                      className={`slider-dot ${index === currentSlide ? 'active' : ''}`}
                      onClick={() => goToSlide(index)}
                      aria-label={`Go to category ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="no-categories">
              <FaExclamationTriangle />
              <p>No categories available</p>
            </div>
          )}
        </div>
      </section>

      {/* Featured Packs Section */}
      <section className="featured-packs">
        <div className="section-header">
          <h2><FaGift /> Featured Packs</h2>
          <Link to="/packs" className="view-all">
            View All Packs <FaArrowRight />
          </Link>
        </div>

        {featuredPacks.length > 0 ? (
          <div className="featured-packs-grid">
            {featuredPacks.map(pack => (
              <PackCard key={pack.id} pack={pack} />
            ))}
          </div>
        ) : (
          <div className="empty-section">
            <FaGift size={40} />
            <p>No featured packs available at the moment.</p>
            <Link to="/packs" className="btn btn-outline">Browse All Packs</Link>
          </div>
        )}
      </section>



      {/* About Section */}
      <section className="about-section">
        <div className="section-header">
          <h2>About Nordica Nutrition</h2>
        </div>
        <div className="about-content">
          <div className="about-text">
            <p>
              Nordica Nutrition is your premier destination for high-quality nutrition supplements in Tunisia.
              Based in Jendouba, we offer nationwide delivery with cash on delivery payment options.
            </p>
            <p>
              Our mission is to provide the best nutrition products to help you achieve your fitness and health goals.
              We carefully select our products to ensure quality and effectiveness.
            </p>
            <p>
              With our wide range of supplements, from protein powders to vitamins and weight management products,
              we have everything you need for your fitness journey.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
