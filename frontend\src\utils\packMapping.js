// Utility functions for handling pack mappings in orders

/**
 * Get pack information for an order item
 * @param {Object} orderItem - The order item from the API
 * @param {number} orderId - The order ID
 * @returns {Object|null} Pack information if item is a pack, null otherwise
 */
export const getPackInfo = (orderItem, orderId) => {
  try {
    const mappings = JSON.parse(localStorage.getItem('orderPackMappings') || '{}');
    const orderMappings = mappings[orderId];
    
    if (!orderMappings) {
      return null;
    }
    
    // Create a key to match the order item
    const itemKey = `${orderId}_${orderItem.product?.id || orderItem.product}_${orderItem.quantity}_${orderItem.price}`;
    
    return orderMappings[itemKey] || null;
  } catch (error) {
    console.error('Error retrieving pack mapping:', error);
    return null;
  }
};

/**
 * Check if an order item is a pack
 * @param {Object} orderItem - The order item from the API
 * @param {number} orderId - The order ID
 * @returns {boolean} True if the item is a pack
 */
export const isPackItem = (orderItem, orderId) => {
  // First check if we have pack info stored
  const packInfo = getPackInfo(orderItem, orderId);
  if (packInfo && packInfo.is_pack) {
    return true;
  }
  
  // Fallback to checking item properties
  return orderItem.pack || 
         (orderItem.type === 'pack') || 
         (orderItem.pack_id) || 
         (orderItem.pack_name) ||
         (orderItem.is_pack) ||
         (orderItem.item_type === 'pack');
};

/**
 * Get the display name for an order item (pack name or product name)
 * @param {Object} orderItem - The order item from the API
 * @param {number} orderId - The order ID
 * @returns {string} The display name
 */
export const getItemDisplayName = (orderItem, orderId) => {
  const packInfo = getPackInfo(orderItem, orderId);
  
  if (packInfo && packInfo.pack_name) {
    return packInfo.pack_name;
  }
  
  // Check if it's a pack using other methods
  if (isPackItem(orderItem, orderId)) {
    return orderItem.pack?.name || 
           orderItem.pack_name || 
           orderItem.name || 
           orderItem.product_name || 
           'Pack';
  }
  
  // Regular product
  return orderItem.product?.name || 
         orderItem.product_name || 
         orderItem.name || 
         'Product';
};

/**
 * Get the pack price for an order item
 * @param {Object} orderItem - The order item from the API
 * @param {number} orderId - The order ID
 * @returns {string|number} The pack price or regular price
 */
export const getItemPrice = (orderItem, orderId) => {
  const packInfo = getPackInfo(orderItem, orderId);
  
  if (packInfo && packInfo.pack_price) {
    return packInfo.pack_price;
  }
  
  // Fallback to item price
  return orderItem.pack_price || orderItem.price;
};

/**
 * Get the pack ID for linking
 * @param {Object} orderItem - The order item from the API
 * @param {number} orderId - The order ID
 * @returns {number|null} The pack ID if available
 */
export const getPackId = (orderItem, orderId) => {
  const packInfo = getPackInfo(orderItem, orderId);
  
  if (packInfo && packInfo.pack_id) {
    return packInfo.pack_id;
  }
  
  return orderItem.pack?.id || orderItem.pack_id || null;
};

/**
 * Clean up old pack mappings (optional - call periodically)
 * @param {number} daysToKeep - Number of days to keep mappings
 */
export const cleanupOldMappings = (daysToKeep = 30) => {
  try {
    const mappings = JSON.parse(localStorage.getItem('orderPackMappings') || '{}');
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    
    // This is a simple cleanup - in a real app you'd want to track creation timestamps
    // For now, we'll just keep all mappings since we don't have timestamps
    console.log('Pack mappings cleanup - keeping all mappings for now');
  } catch (error) {
    console.error('Error cleaning up pack mappings:', error);
  }
};
