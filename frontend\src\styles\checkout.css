/* CHECKOUT PAGE - ENHANCED DESIGN */
.checkout-page {
  padding: var(--space-6) var(--space-4);
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
  position: relative;
  overflow-x: hidden;
}

.checkout-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 250px;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.06) 0%, rgba(220, 38, 38, 0.02) 100%);
  z-index: 0;
  border-radius: 0 0 40% 40%;
}

.checkout-page > * {
  position: relative;
  z-index: 1;
}

/* Checkout Header - Enhanced */
.checkout-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6) var(--space-4);
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
  position: relative;
  overflow: hidden;
}

.checkout-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: checkout-shimmer 4s ease-in-out infinite;
}

@keyframes checkout-shimmer {
  0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
  50% { transform: translateX(0%) translateY(0%) rotate(180deg); }
}

.checkout-header h1 {
  font-size: 2.5rem;
  color: var(--white);
  margin-bottom: var(--space-3);
  font-weight: 700;
  position: relative;
}

.checkout-header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--white);
  border-radius: 2px;
}

.checkout-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.checkout-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4);
  align-items: start;
}

.checkout-form-container {
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  padding: var(--space-8);
  border: 1px solid rgba(220, 38, 38, 0.1);
  width: 100%;
}

.checkout-order-summary-sidebar {
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  padding: var(--space-6);
  border: 1px solid rgba(220, 38, 38, 0.1);
  position: sticky;
  top: var(--space-4);
  height: fit-content;
}

.checkout-form-container h2 {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--neutral-200);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--black);
}

.form-control {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: var(--red);
}

.form-control.error {
  border-color: var(--danger-color);
}

.form-error {
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.payment-info {
  margin-top: var(--space-6);
  margin-bottom: var(--space-6);
  background: var(--white);
  border-radius: 8px;
  padding: var(--space-6);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.payment-info h3 {
  margin-bottom: var(--space-4);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--black);
}

.payment-methods-container {
  display: block;
}

.payment-method {
  background: var(--gray-light);
  border-radius: 8px;
  padding: var(--space-4);
  border: 1px solid var(--border);
  transition: all 0.2s;
}

.payment-method.selected {
  background: var(--white);
  border-color: var(--red);
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.1);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 500;
  font-size: 1rem;
  color: var(--black);
  margin-bottom: var(--space-2);
  cursor: pointer;
}

.radio-label input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: var(--red);
  cursor: pointer;
}

.payment-method-icon {
  font-size: 1.2rem;
}

.payment-note {
  margin-top: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
}

.payment-note i {
  color: var(--red);
  font-size: 0.9rem;
  margin-top: 2px;
  flex-shrink: 0;
}



.order-total-summary {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  padding: var(--space-6);
  margin: var(--space-6) 0;
  border: 2px solid rgba(220, 38, 38, 0.1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.order-total-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--red) 0%, var(--red-dark) 100%);
}

.order-total-summary .order-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  font-size: 1.4rem;
  font-weight: 800;
  color: var(--black);
  padding: var(--space-3);
  background: rgba(220, 38, 38, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(220, 38, 38, 0.1);
}

.order-total-summary .order-total-row span:first-child {
  color: var(--red);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.order-total-summary .order-total-row span:first-child::before {
  content: '💰';
  font-size: 1.2rem;
}

.order-total-summary .order-total-row span:last-child {
  background: linear-gradient(135deg, var(--red) 0%, var(--red-dark) 100%);
  color: var(--white);
  padding: var(--space-2) var(--space-4);
  border-radius: 20px;
  font-weight: 900;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Delivery Note Styling */
.order-total-summary .delivery-note {
  background: var(--gray-light);
  border-radius: 6px;
  padding: var(--space-3);
  margin: 0;
  font-size: 0.9rem;
  color: var(--gray);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.order-total-summary .delivery-note i {
  color: var(--red);
  font-size: 0.9rem;
}

.delivery-note {
  margin-top: var(--space-2);
  color: var(--gray);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.delivery-note i {
  color: var(--red);
  font-size: 0.9rem;
}

/* Checkout Order Summary */
.checkout-order-summary {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  margin: 0;
  box-shadow: none;
}

.checkout-order-summary-sidebar .checkout-order-summary {
  background: transparent;
  border: none;
  padding: 0;
  margin-bottom: var(--space-4);
}

.checkout-order-summary h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 2px solid rgba(220, 38, 38, 0.1);
}

.checkout-items-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.checkout-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-3);
  background: rgba(220, 38, 38, 0.02);
  border: 1px solid rgba(220, 38, 38, 0.1);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.checkout-item:hover {
  background: rgba(220, 38, 38, 0.05);
  border-color: rgba(220, 38, 38, 0.2);
}

.checkout-item-info {
  display: flex;
  gap: var(--space-3);
  flex: 1;
}

.checkout-item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(220, 38, 38, 0.1);
  flex-shrink: 0;
}

.checkout-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.checkout-item-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.pack-placeholder {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
}

.checkout-item-details {
  flex: 1;
}

.checkout-item-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--black);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.checkout-pack-badge {
  background: var(--red);
  color: var(--white);
  font-size: 0.6rem;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 6px;
  text-transform: uppercase;
}

.checkout-item-category {
  font-size: 0.8rem;
  color: var(--gray);
  margin-bottom: var(--space-1);
}

.checkout-pack-contents {
  font-size: 0.75rem;
  color: var(--gray);
  line-height: 1.4;
}

.pack-contents-label {
  font-weight: 600;
  color: var(--red);
}

.pack-item {
  color: var(--black);
}

.checkout-item-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.8rem;
}

.checkout-item-quantity {
  font-size: 0.75rem;
  color: var(--gray);
  font-weight: 500;
}

.checkout-item-price {
  font-size: 0.8rem;
  color: var(--black);
  font-weight: 600;
}

.checkout-item-total {
  font-size: 0.85rem;
  color: var(--red);
  font-weight: 700;
}

.checkout-summary-totals {
  border-top: 2px solid rgba(220, 38, 38, 0.1);
  padding-top: var(--space-3);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.checkout-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.checkout-summary-row span:first-child {
  color: var(--gray);
  font-weight: 500;
}

.checkout-summary-row span:last-child {
  color: var(--black);
  font-weight: 600;
}

.free-delivery {
  color: var(--red) !important;
  font-weight: 700 !important;
}

.place-order-btn {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  font-size: var(--font-size-md);
  font-weight: 600;
}

/* Media Queries */

@media (max-width: 992px) {
  .checkout-container {
    padding: var(--space-3);
  }

  .checkout-form-container {
    max-width: 100%;
    padding: var(--space-6);
    margin: 0;
  }

  .payment-methods-container {
    gap: var(--space-3);
  }

  .payment-method {
    padding: var(--space-4);
  }

  .checkout-order-summary {
    padding: var(--space-3);
    margin: var(--space-3) 0;
  }

  .checkout-item {
    flex-direction: column;
    gap: var(--space-3);
  }

  .checkout-item-info {
    width: 100%;
  }

  .checkout-item-pricing {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: rgba(220, 38, 38, 0.05);
    padding: var(--space-2);
    border-radius: 8px;
    border: 1px solid rgba(220, 38, 38, 0.1);
  }

  .checkout-item-image {
    width: 50px;
    height: 50px;
  }

  .checkout-item-name {
    font-size: 0.9rem;
  }

  .checkout-pack-contents {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .checkout-page {
    padding: var(--space-2);
  }

  .checkout-container {
    padding: var(--space-2);
  }

  .checkout-form-container {
    padding: var(--space-4);
    margin: 0;
    border-radius: 16px;
  }

  .payment-info {
    padding: var(--space-4);
    border-radius: 16px;
  }

  .payment-info h3 {
    font-size: 1.2rem;
    margin-bottom: var(--space-4);
  }

  .payment-method {
    padding: var(--space-3);
    border-radius: 12px;
  }

  .radio-label {
    font-size: 1rem;
    gap: var(--space-2);
  }

  .payment-method-icon {
    font-size: 1.1rem;
  }

  .payment-note {
    font-size: 0.9rem;
    padding: var(--space-2);
  }

  .form-section h3 {
    font-size: 1.3rem;
    margin-bottom: var(--space-3);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: var(--space-3);
    font-size: 1rem;
  }

  .order-total-summary {
    padding: var(--space-4);
  }

  .summary-row {
    font-size: 1rem;
    padding: var(--space-2) 0;
  }

  .summary-total {
    font-size: 1.3rem;
  }

  .place-order-btn {
    width: 100%;
    padding: var(--space-4);
    font-size: 1.1rem;
  }

  .guest-checkout-notice {
    padding: var(--space-3);
    margin-bottom: var(--space-3);
  }

  .guest-checkout-notice p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .checkout-page {
    padding: var(--space-1);
  }

  .checkout-container {
    padding: var(--space-1);
  }

  .checkout-form-container {
    padding: var(--space-3);
    border-radius: 12px;
  }

  .payment-info {
    padding: var(--space-3);
    margin-top: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .payment-info h3 {
    font-size: 1.1rem;
    margin-bottom: var(--space-3);
  }

  .payment-method {
    padding: var(--space-2);
  }

  .radio-label {
    font-size: 0.95rem;
  }

  .payment-note {
    font-size: 0.85rem;
    padding: var(--space-2);
  }

  .form-section h3 {
    font-size: 1.2rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: var(--space-2);
    font-size: 0.9rem;
  }

  .order-total-summary {
    padding: var(--space-3);
  }

  .summary-row {
    font-size: 0.9rem;
  }

  .summary-total {
    font-size: 1.2rem;
  }

  .place-order-btn {
    padding: var(--space-3);
    font-size: 1rem;
  }

  .completion-text {
    font-size: 0.8rem;
  }
}

/* Guest Checkout Notice */
.guest-checkout-notice {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.guest-checkout-notice p {
  margin: 0 0 var(--space-3) 0;
  color: #0c4a6e;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Profile Completion Indicator */
.profile-completion-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-top: var(--space-3);
}

.completion-bar {
  flex: 1;
  height: 8px;
  background: rgba(14, 165, 233, 0.2);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.completion-fill {
  height: 100%;
  background: linear-gradient(90deg, #0ea5e9, #06b6d4);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.completion-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.completion-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #0c4a6e;
  white-space: nowrap;
}

/* Responsive Design */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .checkout-container {
    grid-template-columns: 1fr 450px;
    gap: var(--space-8);
    max-width: 1600px;
  }

  .checkout-form-container {
    padding: var(--space-10);
  }

  .checkout-order-summary-sidebar {
    padding: var(--space-8);
  }
}

/* Desktop (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .checkout-container {
    grid-template-columns: 1fr 400px;
    gap: var(--space-6);
  }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .checkout-container {
    grid-template-columns: 1fr 350px;
    gap: var(--space-4);
    padding: var(--space-3);
  }

  .checkout-form-container {
    padding: var(--space-6);
  }

  .checkout-order-summary-sidebar {
    padding: var(--space-5);
  }

  .form-row {
    gap: var(--space-3);
  }

  .checkout-item-image {
    width: 45px;
    height: 45px;
  }

  .checkout-item-name {
    font-size: 0.8rem;
  }

  .checkout-item-category {
    font-size: 0.7rem;
  }
}

/* Mobile Large (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
  .checkout-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
    padding: var(--space-3);
  }

  .checkout-form-container {
    padding: var(--space-5);
    border-radius: 16px;
  }

  .checkout-order-summary-sidebar {
    order: -1; /* Show order summary first on mobile */
    position: static;
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    border-radius: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-group {
    margin-bottom: var(--space-3);
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .checkout-item {
    padding: var(--space-3);
    border-radius: 10px;
  }

  .checkout-item-info {
    width: 100%;
    margin-bottom: var(--space-2);
  }

  .checkout-item-image {
    width: 50px;
    height: 50px;
  }

  .checkout-item-pricing {
    width: 100%;
    justify-content: space-between;
    align-items: center;
    background: rgba(220, 38, 38, 0.05);
    padding: var(--space-2);
    border-radius: 8px;
  }

  .checkout-summary-totals {
    font-size: 0.9rem;
  }

  .order-total-summary {
    padding: var(--space-4);
  }

  .place-order-btn {
    font-size: 1rem;
    padding: var(--space-4);
  }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
  .checkout-container {
    grid-template-columns: 1fr;
    gap: var(--space-3);
    padding: var(--space-2);
  }

  .checkout-form-container {
    padding: var(--space-4);
    border-radius: 12px;
  }

  .checkout-order-summary-sidebar {
    order: -1;
    position: static;
    margin-bottom: var(--space-3);
    padding: var(--space-3);
    border-radius: 12px;
  }

  .checkout-header h2 {
    font-size: 1.3rem;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-group {
    margin-bottom: var(--space-3);
  }

  .form-group label {
    font-size: 0.85rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: var(--space-3);
  }

  .checkout-item {
    padding: var(--space-2);
    border-radius: 8px;
    gap: var(--space-2);
  }

  .checkout-item-info {
    width: 100%;
    margin-bottom: var(--space-2);
  }

  .checkout-item-image {
    width: 40px;
    height: 40px;
  }

  .checkout-item-name {
    font-size: 0.8rem;
    line-height: 1.2;
  }

  .checkout-item-category {
    font-size: 0.7rem;
  }

  .checkout-item-pricing {
    width: 100%;
    justify-content: space-between;
    align-items: center;
    background: rgba(220, 38, 38, 0.05);
    padding: var(--space-2);
    border-radius: 6px;
    font-size: 0.75rem;
  }

  .checkout-summary-totals {
    font-size: 0.85rem;
  }

  .checkout-summary-row {
    padding: var(--space-2) 0;
  }

  .order-total-summary {
    padding: var(--space-3);
  }

  .order-total-row {
    font-size: 1rem;
  }

  .delivery-note {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .place-order-btn {
    font-size: 0.95rem;
    padding: var(--space-3);
    border-radius: 10px;
  }

  /* Compact order summary for very small screens */
  .checkout-order-summary h3 {
    font-size: 1.1rem;
    margin-bottom: var(--space-3);
  }

  .checkout-items-list {
    max-height: 200px;
    overflow-y: auto;
  }
}

/* Additional Mobile Optimizations */
@media (max-width: 767px) {
  /* Improve touch targets */
  .btn {
    min-height: 48px;
    padding: var(--space-3) var(--space-4);
    font-size: 1rem;
  }

  /* Better spacing for mobile */
  .checkout-container {
    padding: var(--space-2);
  }

  /* Optimize form sections */
  .form-section {
    margin-bottom: var(--space-4);
  }

  .form-section h3 {
    font-size: 1.1rem;
    margin-bottom: var(--space-3);
  }

  /* Improve checkout items display */
  .checkout-items-list {
    gap: var(--space-2);
  }

  .checkout-item + .checkout-item {
    margin-top: var(--space-2);
  }

  /* Better total summary display */
  .checkout-summary-totals {
    border-top: 2px solid rgba(220, 38, 38, 0.1);
    padding-top: var(--space-3);
    margin-top: var(--space-3);
  }

  .checkout-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid rgba(220, 38, 38, 0.05);
  }

  .checkout-summary-row:last-child {
    border-bottom: none;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 767px) and (orientation: landscape) {
  .checkout-order-summary-sidebar {
    order: 1; /* Move order summary back to right side in landscape */
  }

  .checkout-container {
    grid-template-columns: 1fr 300px;
    gap: var(--space-3);
  }

  .checkout-items-list {
    max-height: 150px;
    overflow-y: auto;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .checkout-item-placeholder {
    font-size: 1.2rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .checkout-container *,
  .checkout-form-container *,
  .checkout-order-summary-sidebar * {
    transition: none !important;
    animation: none !important;
  }
}
