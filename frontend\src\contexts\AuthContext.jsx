import { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { authAPI, fetchCSRFToken } from '../services/api';
import { clearGuestCart } from '../utils/guestCart';

// Create the context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);


  // Check if user is already logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const userInfoStr = localStorage.getItem('userInfo');
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          
          // Verify the token is still valid
          try {
            const profileResponse = await authAPI.getProfile();
            // Update user info with fresh data from server
            const updatedUserInfo = {
              token: userInfo.token,
              ...profileResponse.data.user,
              user: profileResponse.data.user
            };
            localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo));
            setCurrentUser(updatedUserInfo);
          } catch (error) {
            console.error('AuthContext: Token invalid, logging out');
            localStorage.removeItem('userInfo');
            setCurrentUser(null);
          }
        } else {
          setCurrentUser(null);
        }
      } catch (error) {
        console.error('AuthContext: Error checking auth:', error);
        localStorage.removeItem('userInfo');
        setCurrentUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Fetch CSRF token when the app loads
    fetchCSRFToken().catch(error => {
      console.error('Error fetching initial CSRF token:', error);
    });
  }, []);



  // Login function
  const login = async (username, password) => {
    try {
      setError(null);
      setLoading(true);

      console.log('AuthContext: Login attempt with:', { username, password: '******' });

      // Fetch CSRF token first
      await fetchCSRFToken();

      // Prepare login data
      const loginData = { username, password };
      console.log('AuthContext: Sending login data:', loginData);

      // Make login request
      const response = await authAPI.login(loginData);
      console.log('AuthContext: Login response:', response);

      if (response.data) {
        // Store user info with token
        const userInfo = {
          token: response.data.token,
          ...response.data.user,
          user: response.data.user
        };
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        console.log('AuthContext: User info stored in localStorage');

        // Update current user
        setCurrentUser(userInfo);
        console.log('AuthContext: Current user set');

        return response.data;
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      console.error('AuthContext: Login error:', err);
      console.error('AuthContext: Error response data:', err.response?.data);

      let errorMessage = 'Failed to login';
      if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.response.data.non_field_errors) {
          errorMessage = err.response.data.non_field_errors[0];
        } else {
          // Try to extract error messages from the response
          const errorFields = Object.keys(err.response.data);
          if (errorFields.length > 0) {
            const firstError = err.response.data[errorFields[0]];
            errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
          }
        }
      }

      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setError(null);
      setLoading(true);

      console.log('Registering with data:', userData);

      // Fetch CSRF token first
      await fetchCSRFToken();

      // Register the user
      const response = await authAPI.register(userData);
      console.log('Registration response:', response);

      // The backend automatically logs in the user after registration
      // Store user info with token
      const userInfo = {
        token: response.data.token,
        ...response.data.user,
        user: response.data.user
      };
      localStorage.setItem('userInfo', JSON.stringify(userInfo));
      setCurrentUser(userInfo);

      return response.data;
    } catch (err) {
      console.error('Registration error details:', err);
      console.error('Error response data:', err.response?.data);

      // More detailed error message
      let errorMessage = 'Failed to register';
      if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.response.data.message) {
          errorMessage = err.response.data.message;
        } else if (err.response.data.non_field_errors) {
          errorMessage = err.response.data.non_field_errors[0];
        } else {
          // Try to extract error messages from the response
          const errorFields = Object.keys(err.response.data);
          if (errorFields.length > 0) {
            const firstError = err.response.data[errorFields[0]];
            errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
          }
        }
      }

      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setLoading(true);

      try {
        // Call logout API
        await authAPI.logout();
      } catch (error) {
        console.error('Logout API error:', error);
        // Continue with local logout even if API call fails
      }

      // Clear local storage
      localStorage.removeItem('userInfo');

      // Note: We don't clear guest cart here because:
      // 1. User's items should be saved on the server
      // 2. When they log back in, server cart will be loaded
      // 3. Guest cart will only be used if they're not authenticated

      setCurrentUser(null);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to logout');
    } finally {
      setLoading(false);
    }
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      setError(null);
      setLoading(true);

      const response = await authAPI.updateProfile(profileData);

      // Update user info in local storage
      const updatedUser = {
        ...currentUser,
        first_name: profileData.user.first_name,
        last_name: profileData.user.last_name,
        email: profileData.user.email,
        profile: {
          ...currentUser.profile,
          phone_number: profileData.phone_number,
          address: profileData.address,
          city: profileData.city,
          state: profileData.state,
          zip_code: profileData.zip_code,
        }
      };

      localStorage.setItem('userInfo', JSON.stringify(updatedUser));
      setCurrentUser(updatedUser);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update profile');
      throw err;
    } finally {
      setLoading(false);
    }
  };



  // Context value
  const value = {
    currentUser,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,

    isAuthenticated: !!currentUser,
    isAdmin: currentUser?.is_staff || false,
    user: currentUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
