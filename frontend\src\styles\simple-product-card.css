/* Simple & Beautiful Product Card */
.simple-product-card {
  background: var(--white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%; /* Flexible height like other pages */
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.simple-product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Product Image */
.product-image-wrapper {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.product-image-link {
  display: block;
  width: 100%;
  height: 100%;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.simple-product-card:hover .product-image {
  transform: scale(1.05);
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #adb5bd;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

/* Featured Badge */
.featured-badge {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: var(--white);
  padding: var(--space-1) var(--space-3);
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
  z-index: 2;
  animation: pulse-featured 2s infinite;
}

@keyframes pulse-featured {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.featured-badge svg {
  font-size: 0.8rem;
}

.out-of-stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 700;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 3;
}

/* Product Content */
.product-content {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-title-link {
  text-decoration: none;
  margin-bottom: var(--space-2);
}

.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--black);
  margin: 0;
  line-height: 1.3;
  transition: color 0.2s ease;
}

.product-title-link:hover .product-title {
  color: var(--red);
}

.product-category-link {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 6px;
  padding: var(--space-2) var(--space-3);
  background: var(--white);
  border: 1px solid var(--border);
  position: relative;
  overflow: hidden;
  min-height: 36px;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-category-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);
  transition: left 0.5s ease;
}

.product-category-link:hover {
  transform: translateY(-2px);
  background: var(--red);
  border-color: var(--red);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.25);
  color: var(--white);
}

.product-category-link:hover::before {
  left: 100%;
}

.product-category-link:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

.product-category {
  font-size: inherit;
  color: inherit;
  margin: 0;
  font-weight: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  z-index: 1;
  width: 100%;
  justify-content: space-between;
}

.product-category svg {
  font-size: 0.75rem;
  opacity: 0.8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0) scale(1);
  flex-shrink: 0;
}

.product-category .category-name {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: inherit;
}

.product-category .category-arrow {
  font-size: 0.7rem;
  opacity: 0.8;
  transform: translateX(0) scale(1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.product-category::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--red);
  transition: width 0.2s ease;
}

.product-category-link:hover .product-category {
  color: var(--white);
  font-weight: 600;
}

.product-category-link:hover .product-category svg {
  opacity: 1;
  transform: translateX(1px) scale(1.05);
  color: var(--white);
}

.product-category-link:hover .product-category .category-arrow {
  opacity: 1;
  transform: translateX(2px) scale(1.1);
  color: var(--white);
}

/* Product Availability */
.product-availability {
  margin-bottom: var(--space-3);
}

.availability-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.availability-badge svg {
  font-size: 0.8rem;
}

.availability-badge.in-stock {
  background: linear-gradient(135deg, #10b981, #059669);
  color: var(--white);
  border: 1px solid #047857;
}

.availability-badge.out-of-stock {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: var(--white);
  border: 1px solid #b91c1c;
}

/* Price Section */
.product-price-section {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  margin-top: auto;
}

.product-price {
  font-size: 1.6rem;
  font-weight: 800;
  color: var(--red);
  line-height: 1;
}

.currency {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6c757d;
  background: #f8f9fa;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* Add to Cart Button */
.add-to-cart-button {
  width: 100%;
  padding: var(--space-3);
  background: var(--red);
  color: var(--white);
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.3px;
  box-shadow: 0 3px 8px rgba(220, 38, 38, 0.25);
}

.add-to-cart-button:hover:not(.disabled) {
  background: var(--red-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.add-to-cart-button:active:not(.disabled) {
  transform: translateY(0);
}

.add-to-cart-button.disabled {
  background: #6c757d;
  cursor: not-allowed;
  box-shadow: none;
}

.add-to-cart-button .loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-product-card {
    border-radius: 12px;
  }

  .product-image-wrapper {
    height: 170px;
  }

  .product-content {
    padding: var(--space-3);
  }

  .product-title {
    font-size: 1rem;
  }

  .product-category {
    font-size: 0.75rem;
  }

  .product-price {
    font-size: 1.4rem;
  }

  .currency {
    font-size: 0.8rem;
  }

  .add-to-cart-button {
    padding: var(--space-2) var(--space-3);
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .simple-product-card {
    border-radius: 12px;
  }

  .product-image-wrapper {
    height: 160px;
  }

  .product-content {
    padding: var(--space-3);
  }

  .product-title {
    font-size: 1rem;
    line-height: 1.3;
    margin-bottom: var(--space-2);
  }

  .product-category-link {
    padding: var(--space-2);
    border-radius: 6px;
    font-size: 0.75rem;
    min-height: 32px;
  }

  .product-category {
    gap: var(--space-1);
  }

  .product-category svg {
    font-size: 0.65rem;
  }

  .product-category .category-arrow {
    font-size: 0.6rem;
  }

  .product-category .category-name {
    font-size: 0.75rem;
  }

  .product-category-link:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
  }

  .availability-badge {
    font-size: 0.7rem;
    padding: var(--space-1) var(--space-2);
  }

  .product-price {
    font-size: 1.4rem;
  }

  .currency {
    font-size: 1rem;
  }

  .add-to-cart-button {
    font-size: 0.85rem;
    padding: var(--space-2) var(--space-3);
    margin-top: var(--space-3);
  }

  .featured-badge {
    top: var(--space-2);
    left: var(--space-2);
    padding: var(--space-1) var(--space-2);
    font-size: 0.7rem;
  }

  .out-of-stock-overlay {
    font-size: 1rem;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .simple-product-card {
    border-radius: 10px;
  }

  .product-image-wrapper {
    height: 140px;
  }

  .product-content {
    padding: var(--space-2);
  }

  .product-title {
    font-size: 0.9rem;
  }

  .product-category-link {
    font-size: 0.7rem;
    min-height: 28px;
    padding: var(--space-1);
  }

  .product-price {
    font-size: 1.2rem;
  }

  .add-to-cart-button {
    font-size: 0.8rem;
    padding: var(--space-2);
  }

  .featured-badge {
    font-size: 0.65rem;
    padding: var(--space-1);
  }

  .availability-badge {
    font-size: 0.65rem;
    padding: var(--space-1);
  }
}

/* Grid Layout for Product Cards */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--space-4);
  padding: var(--space-3) 0;
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

/* Featured Products Section - Same as other grids */
.featured-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--space-4);
  padding: var(--space-3) 0;
}

@media (max-width: 768px) {
  .featured-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .featured-products-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

/* Search Results Grid */
.search-page-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-4);
}

@media (max-width: 768px) {
  .search-page-results {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .search-page-results {
    grid-template-columns: 1fr;
  }
}
