/* NOT FOUND PAGE - CLEAN & SIMPLE */

/* Page Container */
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--gray-light) 0%, var(--white) 100%);
  padding: var(--space-4);
}

/* Not Found Container */
.not-found-container {
  text-align: center;
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: var(--space-12);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 100%;
}

/* 404 Number */
.not-found-container h1 {
  font-size: 8rem;
  font-weight: 700;
  color: var(--red);
  margin: 0;
  line-height: 1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Page Not Found Title */
.not-found-container h2 {
  font-size: 2.5rem;
  color: var(--black);
  margin: var(--space-4) 0;
  font-weight: 600;
}

/* Description */
.not-found-container p {
  color: var(--gray);
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: var(--space-8);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Return Button */
.not-found-container .btn {
  padding: var(--space-4) var(--space-8);
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.not-found-container .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Additional Elements */
.not-found-container::before {
  content: '🔍';
  font-size: 3rem;
  display: block;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

/* Animation */
.not-found-container h1 {
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .not-found-page {
    padding: var(--space-2);
  }
  
  .not-found-container {
    padding: var(--space-8);
  }
  
  .not-found-container h1 {
    font-size: 6rem;
  }
  
  .not-found-container h2 {
    font-size: 2rem;
  }
  
  .not-found-container p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .not-found-container {
    padding: var(--space-6);
    border-radius: 8px;
  }
  
  .not-found-container h1 {
    font-size: 4rem;
  }
  
  .not-found-container h2 {
    font-size: 1.5rem;
  }
  
  .not-found-container p {
    font-size: 1rem;
  }
  
  .not-found-container .btn {
    padding: var(--space-3) var(--space-6);
    font-size: 1rem;
  }
}
