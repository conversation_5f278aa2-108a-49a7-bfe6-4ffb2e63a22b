# Nordica Nutrition

A modern e-commerce website for a nutrition store based in Jendouba, Tunisia. This project uses React.js for the frontend, Django REST Framework for the backend, and SQLite for the database.

## Features

- Modern and responsive design with red, black, and grey color scheme
- Product browsing and filtering by categories
- User authentication and profile management
- Wishlist functionality
- Shopping cart
- Checkout with cash on delivery payment option
- Admin dashboard for managing products, orders, and site settings
- Maintenance mode option
- Responsive design for all devices

## Tech Stack

### Frontend
- React.js
- React Router for navigation
- Context API for state management
- CSS for styling

### Backend
- Django REST Framework
- SQLite database
- JWT authentication

## Getting Started

### Prerequisites
- Node.js and npm
- Python 3.x
- Django

### Installation

#### Backend Setup
1. Navigate to the backend directory:
   ```
   cd nordica_backend
   ```

2. Run migrations:
   ```
   python manage.py migrate
   ```

3. Create a superuser:
   ```
   python manage.py createsuperuser
   ```

4. Start the development server:
   ```
   python manage.py runserver
   ```

#### Frontend Setup
1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

## Project Structure

### Backend
- `nordica_backend/` - Django project root
  - `api/` - Django app for REST API
    - `models.py` - Database models
    - `serializers.py` - API serializers
    - `views.py` - API views
    - `urls.py` - API URL routing

### Frontend
- `frontend/` - React project root
  - `src/` - Source code
    - `components/` - Reusable UI components
    - `contexts/` - React context providers
    - `pages/` - Page components
    - `services/` - API services
    - `utils/` - Utility functions

## License
This project is licensed under the MIT License.

## Acknowledgements
- [React](https://reactjs.org/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Font Awesome](https://fontawesome.com/)
- [Google Fonts](https://fonts.google.com/)
