from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'categories', views.CategoryViewSet)
router.register(r'products', views.ProductViewSet)
router.register(r'packs', views.PackViewSet)
router.register(r'cart', views.CartViewSet, basename='cart')
router.register(r'orders', views.OrderViewSet, basename='order')

urlpatterns = [
    path('', include(router.urls)),
    path('auth/login/', views.login_view, name='login'),
    path('auth/register/', views.register_view, name='register'),
    path('auth/logout/', views.logout_view, name='logout'),
    path('auth/change-password/', views.change_password_view, name='change-password'),
    path('users/me/', views.profile_view, name='profile'),
    path('profiles/me/', views.profile_update_view, name='profile-update'),
    path('settings/', views.SiteSettingsView.as_view(), name='settings'),
    path('settings/current/', views.SiteSettingsView.as_view(), name='settings-current'),
]
