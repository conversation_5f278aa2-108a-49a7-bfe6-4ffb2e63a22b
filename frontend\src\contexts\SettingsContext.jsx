import { createContext, useState, useContext, useEffect } from 'react';
import { settingsAPI } from '../services/api';

// Create the context
const SettingsContext = createContext();

// Custom hook to use the settings context
export const useSettings = () => {
  return useContext(SettingsContext);
};

// Provider component
export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState({
    maintenance_mode: false,
    phone_number: '',
    email: '',
    facebook_url: '',
    instagram_url: '',
    tiktok_url: '',
    address: '',
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load settings on mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from API
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await settingsAPI.getSettings();
      setSettings(response.data);
      
      return response.data;
    } catch (err) {
      setError('Failed to load settings');
      console.error(err);
      
      // Set default settings in case of error
      setSettings({
        maintenance_mode: false,
        phone_number: '',
        email: '',
        facebook_url: '',
        instagram_url: '',
        tiktok_url: '',
        address: '',
      });
    } finally {
      setLoading(false);
    }
  };

  // Update settings
  const updateSettings = async (updatedSettings) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await settingsAPI.updateSettings(updatedSettings);
      setSettings(response.data);
      
      return response.data;
    } catch (err) {
      setError('Failed to update settings');
      console.error(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    settings,
    loading,
    error,
    fetchSettings,
    updateSettings,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
